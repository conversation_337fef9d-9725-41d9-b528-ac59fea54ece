{"name": "epic-padel-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --experimental-https", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-ses": "^3.826.0", "@hookform/resolvers": "^3.10.0", "@microsoft/clarity": "^1.0.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "autoprefixer": "^10.4.20", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "firebase": "^11.8.1", "framer-motion": "^12.12.1", "gsap": "^3.13.0", "immer": "^10.1.1", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "next": "15.3.2", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-day-picker": "^9.5.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "react-scan": "^0.3.3", "react-toastify": "^10.0.6", "recharts": "^2.15.1", "sonner": "^2.0.5", "swr": "^2.3.4", "tailwind-merge": "^3.3.0", "use-sync-external-store": "^1.4.0", "vaul": "^1.1.2", "zod": "^3.25.51", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/crypto-js": "^4.2.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^4", "typescript": "^5"}}