"use client";

import { appleLogin, googleLogin, otpLogin, sendOTP } from "@/api/auth-service";
import { useFacebookPixel } from "@/hooks/use-facebook-pixel";
import { useUIStore } from "@/store/ui-store";
import * as Dialog from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import Loader from "../loader";

interface LoginDialogProps {}

const LoginDialog: React.FC<LoginDialogProps> = () => {
  const { isLoginDialogOpen, openLoginDialog, closeLoginDialog, openMembershipDialog } =
    useUIStore();
  const [phoneNumber, setPhoneNumber] = useState("");
  const [countryCode, setCountryCode] = useState("+1");
  const [showOTP, setShowOTP] = useState(false);
  const [otpValues, setOtpValues] = useState(["", "", "", ""]);
  const [phoneError, setPhoneError] = useState("");
  const [otpError, setOtpError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [timer, setTimer] = useState(600);
  const [isTimerActive, setIsTimerActive] = useState(false);
  const { trackCompleteRegistration } = useFacebookPixel();

  const otpInputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isTimerActive && timer > 0) {
      interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
    } else if (timer === 0) {
      setIsTimerActive(false);
    }
    return () => clearInterval(interval);
  }, [isTimerActive, timer]);

  // Format timer display
  const formatTimer = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  // Phone validation
  const validateUSPhone = (phone: string) => {
    const phoneRegex = /^[0-9]{10}$/;
    return phoneRegex.test(phone);
  };

  const validateUAEPhone = (phone: string) => {
    const phoneRegex = /^[0-9]{9}$/;
    return phoneRegex.test(phone);
  };

  // Handle phone input
  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, "");
    const maxLength = countryCode === "+1" ? 10 : 9;
    if (value.length <= maxLength) {
      setPhoneNumber(value);
      setPhoneError("");
    }
  };

  // Handle OTP input
  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) return;

    const newOtpValues = [...otpValues];
    newOtpValues[index] = value;
    setOtpValues(newOtpValues);
    setOtpError("");

    // Auto-focus next input
    if (value && index < 3) {
      otpInputRefs.current[index + 1]?.focus();
    }
  };

  // Handle OTP backspace
  const handleOtpKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === "Backspace" && !otpValues[index] && index > 0) {
      otpInputRefs.current[index - 1]?.focus();
    }
  };

  // Send OTP
  const handleSendOTP = async () => {
    const isValidPhone =
      countryCode === "+1" ? validateUSPhone(phoneNumber) : validateUAEPhone(phoneNumber);

    if (!isValidPhone) {
      setPhoneError("Invalid phone number");
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Replace with actual API call
      console.log("Sending OTP to:", countryCode + phoneNumber);

      // Simulate API call
      const response = await sendOTP({
        phone: phoneNumber,
        country: countryCode,
      });

      if (!response || response?.status !== "success") {
        setPhoneError(response.message);
        return;
      }

      setShowOTP(true);
      setTimer(600);
      setIsTimerActive(true);
    } catch (error: any) {
      toast.error(error?.message || "Failed to send OTP");
      setPhoneError("Failed to send OTP. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Verify OTP
  const handleVerifyOTP = async () => {
    const otp = otpValues.join("");
    if (otp.length !== 4) {
      setOtpError("Please enter a valid 4-digit OTP");
      return;
    }

    setIsLoading(true);
    try {
      // TODO: Replace with actual API call
      console.log("Verifying OTP:", otp);

      // Simulate API call
      const response = await otpLogin({
        phone: phoneNumber,
        country: countryCode,
        otp,
      });

      if (!response || response?.status !== "success") {
        setOtpError(response.message);
        return;
      }

      // Handle successful login
      closeLoginDialog();
      resetForm();
      checkForStoredPlan();
      console.log("login successful", response);
      if (response?.registration) {
        trackCompleteRegistration();
        // sendFacebookConversionApiEvent("CompleteRegistration", {
        //   phone: countryCode || "" + phoneNumber || "",
        //   email: "<EMAIL>",
        // });
      }
    } catch (error) {
      setOtpError("Invalid OTP. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Check for stored plan after login
  const checkForStoredPlan = () => {
    const planId = localStorage.getItem("planId");
    const planName = localStorage.getItem("planName");
    const planPrice = localStorage.getItem("planPrice");
    const planIsYearly = localStorage.getItem("planIsYearly");

    if (planId && planPrice && planIsYearly) {
      // Clear stored plan data
      localStorage.removeItem("planId");
      localStorage.removeItem("planName");
      localStorage.removeItem("planPrice");
      localStorage.removeItem("planIsYearly");

      // Open membership dialog with stored plan
      openMembershipDialog({
        id: parseInt(planId),
        name: planName || "Selected Plan",
        price: parseFloat(planPrice),
        isYearly: planIsYearly === "true",
      });
    }
  };

  // Handle social login
  const handleSocialLogin = async (provider: "google" | "apple") => {
    console.log(`Login with ${provider}`);
    if (provider === "google") {
      const data = await googleLogin();
      console.log("google data", data);
      closeLoginDialog();
      resetForm();
      checkForStoredPlan();
      if (data?.registration) {
        trackCompleteRegistration();
        // sendFacebookConversionApiEvent("CompleteRegistration", {
        //   email: data?.user?.user_email?.email || "",
        //   phone: "**********",
        // });
      }
    } else if (provider === "apple") {
      const data = await appleLogin();
      console.log("apple data", data);
      closeLoginDialog();
      resetForm();
      checkForStoredPlan();
      if (data?.registration) {
        trackCompleteRegistration();
        // sendFacebookConversionApiEvent("CompleteRegistration", {
        //   email: data?.user?.user_email?.email || "",
        //   phone: "**********",
        // });
      }
    }
  };

  // Reset form
  const resetForm = () => {
    setPhoneNumber("");
    setCountryCode("+1");
    setShowOTP(false);
    setOtpValues(["", "", "", ""]);
    setPhoneError("");
    setOtpError("");
    setIsLoading(false);
    setTimer(600);
    setIsTimerActive(false);
  };

  // Handle dialog close
  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      resetForm();
      closeLoginDialog();
    } else {
      openLoginDialog();
    }
  };

  return (
    <Dialog.Root open={isLoginDialogOpen} onOpenChange={handleOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[2000] bg-black/50" />
        <Dialog.Content className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-[2001] w-[96%] max-w-5xl translate-x-[-50%] translate-y-[-50%] rounded-[20px] bg-white p-3 shadow-lg duration-200">
          <Dialog.Close className="focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-2 right-2 rounded-sm opacity-70 ring-0 transition-opacity hover:opacity-100 focus:ring-0 focus:outline-none disabled:pointer-events-none">
            <X className="h-8 w-8 text-gray-600 outline-none" />
            <span className="sr-only">Close</span>
          </Dialog.Close>

          <Dialog.Title className="sr-only">Login</Dialog.Title>

          <div className="flex min-h-[500px] flex-col md:flex-row lg:min-h-[600px]">
            {/* Image Section */}
            <Image
              src={showOTP ? "/imgs/page/login/banner.png" : "/imgs/login-screen.png"}
              alt="Login"
              title="Login"
              width={500}
              height={500}
              className="hidden min-h-[200px] flex-1 rounded-l-[20px] bg-cover bg-center bg-no-repeat md:block md:min-h-[500px]"
              loading="lazy"
            />

            {/* Form Section */}
            <div className="flex flex-1 flex-col justify-center p-8">
              <div className="mb-8 text-center">
                <Image
                  src="/imgs/template/logo-green.svg"
                  alt="Epic"
                  title="Epic"
                  className="mx-auto mb-4 h-12"
                  width={100}
                  height={48}
                  loading="lazy"
                />
                <h4 className="mb-2 text-2xl font-bold text-gray-900">
                  Join the Epic Padel Community 🎾
                </h4>
                <p className="text-sm text-gray-600">
                  Unlock unlimited access to premium Padel courts and exclusive member benefits.
                  Sign up today and start playing!
                </p>
              </div>

              {!showOTP ? (
                // Phone Input Screen
                <>
                  <div className="mb-4">
                    <div className="flex overflow-hidden rounded-full border border-gray-300">
                      <select
                        value={countryCode}
                        onChange={(e) => setCountryCode(e.target.value)}
                        className="border-r border-gray-300 bg-gray-50 px-3 py-3 text-sm focus:outline-none"
                      >
                        <option value="+1">🇺🇸 +1</option>
                        {/* <option value="+971">🇦🇪 +971</option> */}
                      </select>
                      <input
                        type="tel"
                        placeholder="Mobile Number"
                        value={phoneNumber}
                        onChange={handlePhoneChange}
                        maxLength={countryCode === "+1" ? 10 : 9}
                        className="flex-1 px-3 py-3 text-black focus:outline-none"
                      />
                    </div>
                    {phoneError && <div className="mt-1 text-sm text-red-500">{phoneError}</div>}
                  </div>

                  <button
                    onClick={handleSendOTP}
                    disabled={isLoading}
                    className="bg-primary hover:bg-primary/90 mb-6 w-full cursor-pointer rounded-full py-3 font-medium text-white disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    {isLoading ? <Loader /> : "Send OTP"}
                  </button>
                </>
              ) : (
                // OTP Input Screen
                <>
                  <div className="mb-4">
                    <div className="mb-4 flex justify-center space-x-3">
                      {otpValues.map((value, index) => (
                        <input
                          key={index}
                          ref={(el) => {
                            otpInputRefs.current[index] = el;
                          }}
                          type="text"
                          value={value}
                          onChange={(e) => handleOtpChange(index, e.target.value)}
                          onKeyDown={(e) => handleOtpKeyDown(index, e)}
                          maxLength={1}
                          className="focus:ring-primary h-12 w-12 rounded-lg border border-gray-300 text-center text-black focus:ring-2 focus:outline-none"
                        />
                      ))}
                    </div>
                    {otpError && (
                      <div className="mb-2 text-center text-sm text-red-500">{otpError}</div>
                    )}
                    <div className="text-center text-sm text-gray-600">
                      Code will expire in <span className="font-medium">{formatTimer(timer)}</span>{" "}
                      seconds.
                    </div>
                  </div>

                  <button
                    onClick={handleVerifyOTP}
                    disabled={isLoading}
                    className="bg-primary hover:bg-primary/90 mb-6 w-full cursor-pointer rounded-full py-3 font-medium text-white disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    {isLoading ? <Loader /> : "Verify OTP"}
                  </button>
                </>
              )}

              {!showOTP && (
                <>
                  <div className="relative mb-6">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-gray-300" />
                    </div>
                    <div className="relative flex justify-center text-sm">
                      <span className="bg-white px-2 text-gray-500">Or</span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <button
                      onClick={() => handleSocialLogin("google")}
                      className="flex w-full cursor-pointer items-center justify-center space-x-3 rounded-full border border-gray-300 px-4 py-3 transition-colors hover:bg-gray-50"
                    >
                      <Image
                        src="/imgs/google.svg"
                        alt="Google"
                        width={20}
                        height={20}
                        title="Google"
                        className="h-5 w-5"
                        loading="lazy"
                      />
                      <span className="text-gray-700">Sign in with Google</span>
                    </button>
                    <button
                      onClick={() => handleSocialLogin("apple")}
                      className="flex w-full cursor-pointer items-center justify-center space-x-3 rounded-full bg-black px-4 py-3 text-white transition-colors hover:bg-gray-800"
                    >
                      <Image
                        src="/imgs/apple.svg"
                        alt="Apple"
                        width={20}
                        height={20}
                        title="Apple"
                        className="h-5 w-5"
                        loading="lazy"
                      />
                      <span>Sign in with Apple</span>
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default LoginDialog;
