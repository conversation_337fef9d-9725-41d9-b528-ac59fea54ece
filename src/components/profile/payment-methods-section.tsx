"use client";

import { useGetPaymentMethods } from "@/api/payment-service";
import { getCardBrandIcon } from "@/libs/utils";
import { Plus } from "lucide-react";
import { useMemo, useState } from "react";
import PaymentMethodsSkeleton from "./skeletons/payment-methods-skeleton";
import AddPaymentMethodDialog from "./payment-methods/add-payment-method-dialog";
import DeletePaymentMethod from "./payment-methods/delete-payment-method";
import SetDefaultPaymentMethod from "./payment-methods/set-default-payment-method";

interface PaymentMethod {
  id: string;
  brand: string;
  last_number: string;
  is_default: boolean;
}

interface PaymentMethodsSectionProps {
  activeSection: string;
}

const PaymentMethodsSection: React.FC<PaymentMethodsSectionProps> = ({ activeSection }) => {
  const [showAddPaymentDialog, setShowAddPaymentDialog] = useState(false);
  const { paymentMethodList, paymentMethodLoading } = useGetPaymentMethods();

  const typedPaymentMethods = useMemo((): PaymentMethod[] => {
    return (paymentMethodList || []) as PaymentMethod[];
  }, [paymentMethodList]);

  return (
    <div
      className={`flex-1 rounded-lg bg-white p-6 ${
        activeSection === "payment-methods-section" ? "" : "hidden"
      }`}
      id="payment-methods-section"
    >
      <h2 className="mb-6 text-2xl font-bold text-gray-900">Payment Methods</h2>

      {paymentMethodLoading ? (
        <PaymentMethodsSkeleton />
      ) : (
        <div className="space-y-4">
          {typedPaymentMethods.length === 0 ? (
            <div className="flex items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-8 text-gray-500">
              <span>No payment methods available</span>
            </div>
          ) : (
            typedPaymentMethods.map((method) => (
              <div
                key={method.id}
                className="flex items-center justify-between rounded-lg border border-gray-200 p-4"
              >
                <div className="flex items-center space-x-4">
                  <img
                    src={getCardBrandIcon(method.brand)}
                    alt={`${method.brand} logo`}
                    className="h-8 w-auto"
                  />
                  <div>
                    <p className="font-medium">
                      {method.brand.toUpperCase()} **** {method.last_number}
                    </p>
                    {method.is_default && <p className="text-sm text-gray-500">Default</p>}
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <SetDefaultPaymentMethod
                    paymentMethodId={method.id}
                    isDefault={method.is_default}
                  />
                  <DeletePaymentMethod paymentMethodId={method.id} />
                </div>
              </div>
            ))
          )}

          <button
            onClick={() => setShowAddPaymentDialog(true)}
            className="text-primary hover:text-primary/80 flex items-center space-x-2"
          >
            <Plus />
            <span>Add Payment Method</span>
          </button>
        </div>
      )}

      <AddPaymentMethodDialog
        isOpen={showAddPaymentDialog}
        onClose={() => setShowAddPaymentDialog(false)}
      />
    </div>
  );
};

export default PaymentMethodsSection;
