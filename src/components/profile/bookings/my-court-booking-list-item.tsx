import React, { useMemo } from "react";
import { cn } from "@/libs/utils";
import { MoreHorizontal, MapPin } from "lucide-react";
import { Button } from "@/components/ui/button";
import { CourtBookingItem } from "@/types/booking";

interface MyCourtBookingListItemProps {
  booking?: CourtBookingItem;
  className?: string;
  onViewDetails?: (booking: CourtBookingItem) => void;
}

const MyCourtBookingListItem: React.FC<MyCourtBookingListItemProps> = ({
  booking,
  className,
  onViewDetails,
}) => {
  // Default booking data for fallback
  const defaultBooking: CourtBookingItem = {
    id: 1,
    booking_id: "2312",
    court_name: "Tyson court 1",
    date: "2024-04-24",
    start_time: "13:00",
    end_time: "14:00",
    status: "confirmed",
    booking_type: "Court Booking",
  };

  const bookingData = booking || defaultBooking;

  // Format date and time
  const formattedDate = useMemo(() => {
    if (!bookingData.date) return "Date not available";

    try {
      const date = new Date(bookingData.date);
      return date.toLocaleDateString("en-US", {
        weekday: "long",
        day: "numeric",
        month: "short",
      });
    } catch {
      return "Invalid date";
    }
  }, [bookingData.date]);

  const formattedTime = useMemo(() => {
    if (!bookingData.start_time || !bookingData.end_time) return "Time not available";

    try {
      const formatTime = (time: string) => {
        const [hours, minutes] = time.split(":");
        const date = new Date();
        date.setHours(parseInt(hours), parseInt(minutes));
        return date.toLocaleTimeString("en-US", {
          hour: "numeric",
          minute: "2-digit",
          hour12: true,
        });
      };

      return `${formatTime(bookingData.start_time)} - ${formatTime(bookingData.end_time)}`;
    } catch {
      return `${bookingData.start_time} - ${bookingData.end_time}`;
    }
  }, [bookingData.start_time, bookingData.end_time]);

  // Status styling
  const statusConfig = {
    confirmed: {
      bg: "bg-[#f8ffd6]",
      border: "outline-[#c0e702]",
      text: "text-[#1c5534]",
    },
    pending: {
      bg: "bg-yellow-50",
      border: "outline-yellow-400",
      text: "text-yellow-700",
    },
    cancelled: {
      bg: "bg-red-50",
      border: "outline-red-400",
      text: "text-red-700",
    },
    completed: {
      bg: "bg-gray-50",
      border: "outline-gray-400",
      text: "text-gray-700",
    },
  };

  const currentStatus = statusConfig[bookingData.status || "confirmed"];

  return (
    <div
      className={cn(
        "relative w-full rounded-2xl border border-gray-200 bg-white p-4 shadow-sm transition-all duration-200 hover:border-gray-300 hover:shadow-md",
        "focus-within:ring-opacity-20 focus-within:ring-2 focus-within:ring-blue-500",
        className
      )}
    >
      {/* Header with booking ID and menu */}
      <div className="mb-3 flex items-center justify-between">
        <div className="flex items-center gap-1 text-sm">
          <span className="text-gray-400">Booking</span>
          <span className="font-medium text-gray-900">
            #{bookingData.booking_id || bookingData.id}
          </span>
        </div>

        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 hover:bg-gray-100"
          onClick={() => onViewDetails?.(bookingData)}
          aria-label="View booking details"
        >
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </div>

      {/* Divider */}
      <div className="mb-4 h-px w-full bg-gradient-to-r from-transparent via-gray-200 to-transparent" />

      {/* Main content */}
      <div className="space-y-1">
        {/* Court name and details */}
        <div className="space-y-1">
          <h3 className="text-lg leading-tight font-medium text-gray-900">
            {bookingData.court_name || "Court name not available"}
          </h3>
          <p className="text-sm text-gray-600">{formattedDate}</p>
          <p className="text-sm text-gray-600">{formattedTime}</p>
        </div>

        {/* Bottom section with status and action button */}
        <div className="flex items-center justify-between pt-2">
          {/* Status badge */}
          <div
            className={cn(
              "inline-flex items-center rounded-full px-3 py-1 text-sm font-normal outline-1 outline-offset-[-1px]",
              currentStatus.bg,
              currentStatus.border,
              currentStatus.text
            )}
          >
            {bookingData.booking_type || "Court Booking"}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MyCourtBookingListItem;
