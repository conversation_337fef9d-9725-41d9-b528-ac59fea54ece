import { useGetMyCourtBookingList } from "@/api/my-booking-service";
import React, { useCallback, useMemo } from "react";
import MyCourtBookingListItem from "./my-court-booking-list-item";
import { Skeleton } from "@/components/ui/skeleton";
import { CourtBookingItem } from "@/types/booking";

interface CourtBookingSectionProps {
  timeFilter: "upcoming" | "past";
}

const MyCourtBookingSection: React.FC<CourtBookingSectionProps> = ({ timeFilter }) => {
  const {
    myCourtBookingList,
    myCourtBookingListLoading,
    myCourtBookingListError,
    myCourtBookingListEmpty,
  } = useGetMyCourtBookingList({ page: 1, limit: 10 });

  // Filter bookings based on timeFilter
  const filteredBookings = useMemo(() => {
    if (!myCourtBookingList || !Array.isArray(myCourtBookingList)) return [];

    const now = new Date();
    return myCourtBookingList.filter((booking: any) => {
      if (!booking.date && !booking.start_time) return false;

      try {
        const bookingDate = new Date(booking.date || booking.start_time);
        if (timeFilter === "upcoming") {
          return bookingDate >= now;
        } else {
          return bookingDate < now;
        }
      } catch {
        // If date parsing fails, include in upcoming by default
        return timeFilter === "upcoming";
      }
    });
  }, [myCourtBookingList, timeFilter]);

  // Handle view details
  const handleViewDetails = useCallback((booking: CourtBookingItem) => {
    // TODO: Navigate to booking details page or open modal
    console.log("View details for booking:", booking);
  }, []);

  // Loading skeleton
  if (myCourtBookingListLoading) {
    return (
      <div className="space-y-4">
        <div className="mb-4 text-sm text-gray-600">Loading {timeFilter} bookings...</div>
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="space-y-3 rounded-2xl border border-gray-200 p-4">
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-6 w-6 rounded" />
            </div>
            <Skeleton className="h-px w-full" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
              <Skeleton className="h-4 w-1/3" />
            </div>
            <div className="flex items-center justify-between pt-2">
              <Skeleton className="h-6 w-24 rounded-full" />
              <Skeleton className="h-8 w-28 rounded" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Error state
  if (myCourtBookingListError) {
    return (
      <div className="py-8 text-center">
        <div className="mb-2 text-red-600">Error loading bookings</div>
        <div className="text-sm text-gray-500">
          {myCourtBookingListError.message || "Something went wrong"}
        </div>
      </div>
    );
  }

  // Empty state
  if (myCourtBookingListEmpty || filteredBookings.length === 0) {
    return (
      <div className="py-8 text-center">
        <div className="mb-2 text-gray-600">No {timeFilter} bookings found</div>
        <div className="text-sm text-gray-500">
          {timeFilter === "upcoming"
            ? "You don't have any upcoming court bookings."
            : "You don't have any past court bookings."}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-4">
      <div className="mb-4 text-sm text-gray-600">
        Showing {filteredBookings.length} {timeFilter} booking
        {filteredBookings.length !== 1 ? "s" : ""}
      </div>

      {/* Debug info - remove in production */}
      {/* {process.env.NODE_ENV === "development" && (
        <details className="mb-4">
          <summary className="cursor-pointer text-xs text-gray-400">Debug: Raw API Data</summary>
          <pre className="mt-2 overflow-auto rounded bg-gray-100 p-2 text-xs">
            {JSON.stringify(myCourtBookingList, null, 2)}
          </pre>
        </details>
      )} */}

      <div className="flex max-h-[400px] w-full flex-col gap-2 overflow-y-auto">
        {filteredBookings.map((booking: any, index: number) => (
          <MyCourtBookingListItem
            key={booking.id || index}
            booking={booking}
            onViewDetails={handleViewDetails}
          />
        ))}
      </div>
    </div>
  );
};

export default MyCourtBookingSection;
