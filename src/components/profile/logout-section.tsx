
"use client";

import { useAuthStore } from "@/store/auth-store";
import { useRouter } from "next/navigation";
import React from "react";

interface LogoutSectionProps {
  activeSection: string;
}

const LogoutSection: React.FC<LogoutSectionProps> = ({ activeSection }) => {
  const { logout } = useAuthStore();
  const router = useRouter();

  const handleLogout = () => {
    logout();
    router.push("/");
  };

  return (
    <div
      className={`flex-1 rounded-lg bg-white p-6 ${
        activeSection === "logout-section" ? "" : "hidden"
      }`}
      id="logout-section"
    >
      <h2 className="mb-6 text-2xl font-bold text-gray-900">Logout</h2>
      <p className="mb-6 text-gray-500">Are you sure you want to logout?</p>
      <button
        className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none"
        onClick={handleLogout}
      >
        Logout
      </button>
    </div>
  );
};

export default LogoutSection;
