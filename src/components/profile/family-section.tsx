
"use client";

import {
  FamilyMember,
  getFamilyMembers,
} from "@/api/profile-service";
import AddCorporateMemberDialog from "@/components/dialogs/add-corporate-member-dialog";
import AddMemberDialog from "@/components/dialogs/add-member-dialog";
import { modifyDate } from "@/libs/utils";
import { useAuthStore } from "@/store/auth-store";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import FamilySkeleton from "./skeletons/family-skeleton";

interface FamilySectionProps {
  activeSection: string;
}

const FamilySection: React.FC<FamilySectionProps> = ({ activeSection }) => {
  const { user } = useAuthStore();
  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([]);
  const [isLoadingMembers, setIsLoadingMembers] = useState(false);
  const [showAddMemberDialog, setShowAddMemberDialog] = useState(false);
  const [showAddCorporateMemberDialog, setShowAddCorporateMemberDialog] =
    useState(false);

  useEffect(() => {
    if (activeSection === "family-section") {
      loadFamilyMembers();
    }
  }, [activeSection]);

  const loadFamilyMembers = async () => {
    setIsLoadingMembers(true);
    try {
      const members = await getFamilyMembers();
      setFamilyMembers(members);
    } catch (error: any) {
      console.error("Error loading family members:", error);
      toast.error(error.message || "Failed to load family members");
    } finally {
      setIsLoadingMembers(false);
    }
  };

  const handleAddMember = () => {
    if (user?.is_corporate_root_user) {
      setShowAddCorporateMemberDialog(true);
    }
    if (!user?.is_corporate_user) {
      setShowAddMemberDialog(true);
    }
  };

  return (
    <div
      className={`flex-1 rounded-lg bg-white p-6 ${
        activeSection === "family-section" ? "" : "hidden"
      }`}
      id="family-section"
    >
      <div className="mb-6 flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">{` ${
          user?.is_corporate_user ? "Corporate Membership" : "Family Membership"
        }`}</h2>
        {((!user?.is_corporate_root_user && !user?.is_corporate_user) ||
          user?.is_corporate_root_user) && (
          <button
            className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
            onClick={handleAddMember}
            disabled={
              isLoadingMembers ||
              user?.userCurrentPackage?.is_default ||
              (user?.is_corporate_root_user &&
                familyMembers.length >=
                  user?.userCurrentPackage?.corporate_email_limit!)
            }
          >
            Add Member
          </button>
        )}
      </div>

      {user?.is_corporate_root_user &&
        familyMembers.length >=
          user?.userCurrentPackage?.corporate_email_limit! && (
            <div className="my-2 text-sm font-medium text-red-600">
              You have reached the maximum number of members allowed for your plan.
            </div>
          )}

      {isLoadingMembers ? (
        <FamilySkeleton />
      ) : familyMembers.length === 0 ? (
        <div className="py-12 text-center">
          <p className="text-gray-500">
            {user?.userCurrentPackage?.is_default
              ? "Please upgrade your membership to add members"
              : "You don't have any members yet"}
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {familyMembers.map((member) => (
            <div
              key={member.id}
              className="rounded-lg border border-gray-200 bg-white p-6"
            >
              <div className="flex justify-between gap-4">
                <div className="min-w-30 lg:min-w-50">
                  <div className="text-sm font-medium text-gray-500">Name</div>
                  <div className="mt-1 truncate text-lg font-semibold text-gray-900 capitalize">
                    {member?.user?.name || "N/A"}
                  </div>
                </div>
                {!user?.is_corporate_user && (
                  <div>
                    <div className="text-sm font-medium text-gray-500">
                      Amount Paid
                    </div>
                    <div className="mt-1 text-lg font-semibold text-gray-900">
                      ${member?.amount_paid || "N/A"}
                    </div>
                  </div>
                )}
                <div>
                  <div className="text-sm font-medium text-gray-500">
                    Coverage Date
                  </div>
                  <div className="mt-1 text-lg font-semibold text-gray-900">
                    {modifyDate(member.start_date)} -{" "}
                    {modifyDate(member.end_date)}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      <AddMemberDialog
        isOpen={showAddMemberDialog}
        onClose={() => setShowAddMemberDialog(false)}
        onMemberAdded={loadFamilyMembers}
      />

      <AddCorporateMemberDialog
        isOpen={showAddCorporateMemberDialog}
        onClose={() => setShowAddCorporateMemberDialog(false)}
        onMemberAdded={loadFamilyMembers}
      />
    </div>
  );
};

export default FamilySection;
