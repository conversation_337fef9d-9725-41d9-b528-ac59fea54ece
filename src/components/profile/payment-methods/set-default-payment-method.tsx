"use client";

import { setDefaultPaymentMethod, useGetPaymentMethods } from "@/api/payment-service";
import Loader from "@/components/loader";
import { useState } from "react";
import { toast } from "sonner";

interface SetDefaultPaymentMethodProps {
  paymentMethodId: string;
  isDefault: boolean;
}

const SetDefaultPaymentMethod: React.FC<SetDefaultPaymentMethodProps> = ({
  paymentMethodId,
  isDefault,
}) => {
  const { revalidatePaymentMethods } = useGetPaymentMethods();
  const [isLoading, setIsLoading] = useState(false);

  const handleSetDefault = async () => {
    setIsLoading(true);
    try {
      const response = await setDefaultPaymentMethod({ paymentMethodId });
      if (response) {
        toast.success("Default payment method updated successfully");
        revalidatePaymentMethods();
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to set default payment method");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleSetDefault}
      disabled={isDefault || isLoading}
      className="text-primary hover:text-primary/80 disabled:cursor-not-allowed disabled:opacity-50"
    >
      {isLoading ? <Loader /> : isDefault ? "Default" : "Set as Default"}
    </button>
  );
};

export default SetDefaultPaymentMethod;
