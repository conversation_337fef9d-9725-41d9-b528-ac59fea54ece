"use client";

import { setDefaultPaymentMethod, useGetPaymentMethods } from "@/api/payment-service";
import Loader from "@/components/loader";
import { Switch } from "@/components/ui/switch";
import { useState } from "react";
import { toast } from "sonner";

interface SetDefaultPaymentMethodProps {
  paymentMethodId: string;
  isDefault: boolean;
}

const SetDefaultPaymentMethod: React.FC<SetDefaultPaymentMethodProps> = ({
  paymentMethodId,
  isDefault,
}) => {
  const { revalidatePaymentMethods } = useGetPaymentMethods();
  const [isLoading, setIsLoading] = useState(false);

  const handleSetDefault = async (checked: boolean) => {
    if (!checked || isDefault) return; // Only allow setting as default, not unsetting

    setIsLoading(true);
    try {
      const response = await setDefaultPaymentMethod({ paymentMethodId });
      if (response) {
        toast.success("Default payment method updated successfully");
        revalidatePaymentMethods();
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to set default payment method");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <p className="text-xs text-gray-500">{isDefault ? "Default" : "Set as Default"}</p>
      <Switch
        checked={isDefault}
        onCheckedChange={handleSetDefault}
        disabled={isLoading}
        aria-label={isDefault ? "Default payment method" : "Set as default payment method"}
        className="data-[state=checked]:bg-primary data-[state=unchecked]:bg-gray-300"
      />
      {isLoading && <Loader />}
    </div>
  );
};

export default SetDefaultPaymentMethod;
