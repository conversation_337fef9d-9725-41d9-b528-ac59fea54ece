"use client";

import { AddPaymentCardForm } from "@/components/payments/add-payment-card-form";
import { <PERSON>eProvider } from "@/components/providers/stripe-provider";
import * as Dialog from "@radix-ui/react-dialog";
import { X } from "lucide-react";

interface AddPaymentMethodDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const AddPaymentMethodDialog: React.FC<AddPaymentMethodDialogProps> = ({ isOpen, onClose }) => {
  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[2000] bg-black/50" />
        <Dialog.Content className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-[2001] w-[96%] max-w-lg translate-x-[-50%] translate-y-[-50%] rounded-[20px] bg-white p-5 shadow-lg duration-200 lg:w-full">
          <div className="flex items-center justify-between">
            <Dialog.Title className="text-lg font-semibold">Add Payment Method</Dialog.Title>
            <Dialog.Close className="focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-2 right-2 rounded-full opacity-70 ring-0 transition-opacity hover:opacity-100 focus:ring-0 focus:outline-none disabled:pointer-events-none">
              <X className="h-8 w-8 text-gray-600 outline-none" />
              <span className="sr-only">Close</span>
            </Dialog.Close>
          </div>
          <StripeProvider>
            <AddPaymentCardForm setOpen={onClose} />
          </StripeProvider>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default AddPaymentMethodDialog;
