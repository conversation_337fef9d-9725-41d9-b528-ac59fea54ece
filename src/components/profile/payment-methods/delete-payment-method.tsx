"use client";

import { deletePaymentMethod, useGetPaymentMethods } from "@/api/payment-service";
import Loader from "@/components/loader";
import { Trash2Icon } from "lucide-react";

import { useState } from "react";
import { toast } from "sonner";

interface DeletePaymentMethodProps {
  paymentMethodId: string;
}

const DeletePaymentMethod: React.FC<DeletePaymentMethodProps> = ({ paymentMethodId }) => {
  const { revalidatePaymentMethods } = useGetPaymentMethods();
  const [isLoading, setIsLoading] = useState(false);

  const handleDelete = async () => {
    setIsLoading(true);
    try {
      const response = await deletePaymentMethod({ paymentMethodId });
      if (response) {
        toast.success("Payment method deleted successfully");
        revalidatePaymentMethods();
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to delete payment method");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleDelete}
      disabled={isLoading}
      className="text-red-600 hover:text-red-700 disabled:cursor-not-allowed disabled:opacity-50"
    >
      {isLoading ? <Loader /> : <Trash2Icon className="h-5 w-5" />}
    </button>
  );
};

export default DeletePaymentMethod;
