
"use client";

import {
  removeProfileImage,
  updateUserProfile,
  uploadProfileImage,
} from "@/api/profile-service";
import { useAuthStore } from "@/store/auth-store";
import * as Dialog from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";

interface ProfileSectionProps {
  user: any;
  activeSection: string;
  openEditModal: (field: string) => void;
}

const ProfileSection: React.FC<ProfileSectionProps> = ({
  user,
  activeSection,
  openEditModal,
}) => {
  const { updateUser } = useAuthStore();
  const [dobValue, setDobValue] = useState("");
  const [showButtons, setShowButtons] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [isDeletingImage, setIsDeletingImage] = useState(false);

  useEffect(() => {
    if (user) {
      setDobValue(user.dob || "");
    }
  }, [user]);

  const handleDobChange = (value: string) => {
    setDobValue(value);
    setShowButtons(true);
  };

  const handleSaveDob = async () => {
    try {
      const response = await updateUserProfile({ dob: dobValue });
      if (response) {
        toast.success(response.message || "DOB updated successfully!");
        updateUser({ dob: dobValue });
        setShowButtons(false);
      }
    } catch (error: any) {
      console.error("Error saving DOB:", error);
      toast.error(error?.errors?.dob?.[0] || "Failed to update DOB");
    }
  };

  const handleCancelDob = () => {
    setDobValue(user?.dob || "");
    setShowButtons(false);
  };

  const handleImageUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      setIsUploadingImage(true);
      try {
        const response = await uploadProfileImage(file);
        updateUser({
          profile_image: { path: response.user.profile_image.path },
        });
        toast.success(response.message || "Profile image uploaded successfully!");
      } catch (error: any) {
        console.error("Error uploading image:", error);
        toast.error(error.message || "Failed to upload image");
      } finally {
        setIsUploadingImage(false);
        event.target.value = "";
      }
    }
  };

  const handleDeleteImage = () => {
    setShowDeleteConfirm(true);
  };

  const confirmDeleteImage = async () => {
    setIsDeletingImage(true);
    try {
      const response = await removeProfileImage();
      if (response) {
        toast.success("Profile image deleted successfully!");
        updateUser({ profile_image: { path: "" } });
      }
      setShowDeleteConfirm(false);
    } catch (error: any) {
      toast.error(error.message || "Failed to delete image");
    } finally {
      setIsDeletingImage(false);
    }
  };

  return (
    <div
      className={`flex-1 rounded-lg bg-white p-6 ${
        activeSection === "profile-section" ? "" : "hidden"
      }`}
      id="profile-section"
    >
      <h2 className="mb-6 text-2xl font-bold text-gray-900">My Profile</h2>
      <div className="mb-8 flex flex-col items-start">
        <div className="relative">
          <Image
            src={user?.profile_image?.path || "/imgs/profile.png"}
            alt="profile picture"
            title="profile picture"
            width={128}
            height={128}
            className="h-32 w-32 rounded-full object-cover"
          />
          <div className="absolute right-0 -bottom-2 flex gap-2">
            {user?.profile_image?.path && (
              <button
                className={`rounded-full bg-white p-2 shadow-md hover:bg-gray-50 ${
                  isDeletingImage ? "cursor-not-allowed opacity-50" : ""
                }`}
                onClick={isDeletingImage ? undefined : handleDeleteImage}
                disabled={isDeletingImage}
              >
                {isDeletingImage ? (
                  <div className="animate-spin">⟳</div>
                ) : (
                  <Image
                    src="/imgs/delete.svg"
                    alt="delete"
                    title="delete"
                    width={20}
                    height={20}
                    className="h-5 w-5"
                  />
                )}
              </button>
            )}
            <button
              className={`rounded-full bg-white p-2 shadow-md hover:bg-gray-50 ${
                isUploadingImage ? "cursor-not-allowed opacity-50" : ""
              }`}
              onClick={
                isUploadingImage
                  ? undefined
                  : () => document.getElementById("image-upload")?.click()
              }
              disabled={isUploadingImage}
            >
              {isUploadingImage ? (
                <div className="animate-spin">⟳</div>
              ) : (
                <Image
                  src="/imgs/edit.svg"
                  alt="edit"
                  title="edit"
                  width={20}
                  height={20}
                  className="h-5 w-5"
                />
              )}
            </button>
          </div>
        </div>
        <input
          type="file"
          id="image-upload"
          accept="image/*"
          className="hidden"
          onChange={handleImageUpload}
        />
      </div>

      <Dialog.Root open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <Dialog.Portal>
          <Dialog.Overlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[2000] bg-black/50" />
          <Dialog.Content className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-[2001] w-full max-w-md translate-x-[-50%] translate-y-[-50%] rounded-[20px] bg-white p-6 shadow-lg duration-200">
            <Dialog.Close className="focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-2 right-2 rounded-sm opacity-70 ring-0 transition-opacity hover:opacity-100 focus:ring-0 focus:outline-none disabled:pointer-events-none">
              <X className="h-8 w-8 text-gray-600 outline-none" />
              <span className="sr-only">Close</span>
            </Dialog.Close>

            <Dialog.Title className="text-lg font-medium text-gray-900">
              Delete Profile Picture
            </Dialog.Title>
            <Dialog.Description className="mt-2 text-sm text-gray-500">
              Are you sure you want to remove your profile picture?
            </Dialog.Description>

            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                className="focus:ring-primary rounded-full border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:outline-none"
                onClick={() => setShowDeleteConfirm(false)}
              >
                Cancel
              </button>
              <button
                type="button"
                className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                onClick={confirmDeleteImage}
                disabled={isDeletingImage}
              >
                {isDeletingImage ? "Deleting..." : "Yes, Remove"}
              </button>
            </div>
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>

      <div className="space-y-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Email
          </label>
          <div className="relative">
            <input
              type="email"
              className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
              value={user?.user_email?.email || ""}
              readOnly
              placeholder="Enter your email"
            />
            {!user?.user_email?.email && (
              <Image
                width={20}
                height={20}
                onClick={() => openEditModal("email")}
                src="/imgs/edit.svg"
                alt="edit"
                title="edit"
                className="absolute top-1/2 right-3 h-5 w-5 -translate-y-1/2 cursor-pointer"
              />
            )}
          </div>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Phone Number
          </label>
          <div className="relative">
            <input
              type="tel"
              className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
              value={
                user?.user_phone
                  ? `${user.user_phone.country_code}${user.user_phone.phone}`
                  : ""
              }
              readOnly
              placeholder="Enter Your Phone Number"
            />
            {!user?.user_phone && (
              <Image
                width={20}
                height={20}
                src="/imgs/edit.svg"
                alt="edit"
                title="edit"
                onClick={() => openEditModal("phone")}
                className="absolute top-1/2 right-3 h-5 w-5 -translate-y-1/2 cursor-pointer"
              />
            )}
          </div>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Date Of Birth
          </label>
          <input
            type="date"
            id="dob"
            className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
            value={dobValue}
            onChange={(e) => handleDobChange(e.target.value)}
            placeholder="Enter Your Date of Birth"
          />
        </div>

        {showButtons && (
          <div className="flex justify-end space-x-3">
            <button
              className="focus:ring-primary rounded-full border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:outline-none"
              onClick={handleCancelDob}
            >
              Cancel
            </button>
            <button
              className="bg-primary hover:bg-primary/90 focus:ring-primary rounded-full border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none"
              onClick={handleSaveDob}
            >
              Save
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfileSection;
