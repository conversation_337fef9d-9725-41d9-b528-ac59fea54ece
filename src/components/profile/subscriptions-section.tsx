
"use client";

import { openStripePortal } from "@/api/profile-service";
import { modifyDate } from "@/libs/utils";
import { useAuthStore } from "@/store/auth-store";
import { useRouter } from "next/navigation";
import React from "react";
import { toast } from "sonner";

interface SubscriptionsSectionProps {
  activeSection: string;
}

const SubscriptionsSection: React.FC<SubscriptionsSectionProps> = ({
  activeSection,
}) => {
  const { user } = useAuthStore();
  const router = useRouter();

  const handleManagePlan = async () => {
    try {
      if (user?.userCurrentPackage?.is_default) {
        router.push("/membership");
        return;
      }
      await openStripePortal();
    } catch (error: any) {
      console.error("Error opening Stripe portal:", error);
      toast.error(error.message || "Failed to open manage plan portal");
    }
  };

  return (
    <div
      className={`flex-1 rounded-lg bg-white p-6 ${
        activeSection === "subscriptions-section" ? "" : "hidden"
      }`}
      id="subscriptions-section"
    >
      <h2 className="mb-6 text-2xl font-bold text-gray-900">My Subscriptions</h2>
      <div className="space-y-6">
        <div className="rounded-lg border border-gray-200 bg-white p-6">
          <div className="space-y-4">
            <div>
              <h5 className="text-sm font-medium text-gray-500">Plan Name</h5>
              <p className="mt-1 text-lg font-semibold text-gray-900">
                {user?.userCurrentPackage?.name || "No Plan"}
              </p>
            </div>
            <div>
              <h5 className="text-sm font-medium text-gray-500">Join Date</h5>
              <p className="mt-1 text-lg font-semibold text-gray-900">
                {modifyDate(user?.active_purchase_subscription?.start_date!)}
              </p>
            </div>
            <div>
              <h5 className="text-sm font-medium text-gray-500">Expire Date</h5>
              <p className="mt-1 text-lg font-semibold text-gray-900">
                {modifyDate(user?.active_purchase_subscription?.end_date!)}
              </p>
            </div>
          </div>
          <div className="mt-6">
            {(user?.is_corporate_root_user ||
              (!user?.is_corporate_root_user && !user?.is_corporate_user) ||
              user?.userCurrentPackage?.is_default) && (
              <button
                className="bg-primary hover:bg-primary/90 focus:ring-primary w-fit rounded-full border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none"
                onClick={handleManagePlan}
              >
                Manage Plan
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionsSection;
