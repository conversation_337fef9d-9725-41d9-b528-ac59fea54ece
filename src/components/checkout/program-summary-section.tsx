import React, { useMemo } from "react";
import { useBookingStore } from "@/store/booking-store";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertCircle, RefreshCw } from "lucide-react";
import { useLessonBookingEstimate, useProgramBookingEstimate } from "@/hooks/use-booking-estimate";
import SummaryLineItem from "./summary-line-item";
import { useCheckoutStore } from "@/store/checkout-store";

// TypeScript interface for program booking estimate response
export interface ProgramBookingEstimateData {
  payment_type: string;
  total_classes: number;
  booking_price: string;
  amount_due: number;
  tax_rate: number;
  tax_amount: number;
  amount: string;
  total: string;
  total_deduct_amount: string;
  wallet_amount: string;
  wallet_deduct_amount: string;
  card_deduct_amount: string;
  minimum_card_transaction_amount: number;
  program_price: {
    id: number;
    program_id: number;
    package_id: number;
    full_price: string;
    per_class_price: string;
    currency: string;
    created_at: string;
    updated_at: string;
  };
}

export interface ProgramBookingEstimateResponse {
  data: { status: string; message: string; data: ProgramBookingEstimateData };
}

const ProgramSummarySection: React.FC = () => {
  const { programBookingDetails } = useBookingStore();
  const { setTotalAmount } = useCheckoutStore();

  // Prepare lesson estimate request data
  const programEstimateRequestData = useMemo(() => {
    if (!programBookingDetails) return null;

    const payload: any = {
      payment_type: programBookingDetails.sessionType,
      program_id: programBookingDetails.programId,
      use_wallet: 1,
    };

    if (
      programBookingDetails?.selectedClassIds.length > 0 &&
      programBookingDetails.sessionType === "PER_SESSION"
    ) {
      payload["program_class_id"] = programBookingDetails.selectedClassIds;
    }

    return payload;
  }, [programBookingDetails]);

  // Use custom hook for lesson booking estimate
  const { estimateData, isLoading, error, refetch } = useProgramBookingEstimate(
    programEstimateRequestData
  );

  console.log("estimateData", estimateData);

  useMemo(() => {
    if (estimateData) {
      setTotalAmount(parseFloat(estimateData.total));
    }
  }, [estimateData, setTotalAmount]);

  if (!programBookingDetails) {
    return (
      <div className="w-full rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
        <div className="flex items-center justify-center py-8">
          <p className="font-helvetica text-sm text-gray-500">
            No program booking details available
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-white transition-all duration-200">
      {/* Header */}
      <div className="border-b border-gray-100 px-4 py-3 sm:px-6">
        <div className="flex items-center justify-between">
          <h3 className="font-helvetica text-lg font-semibold text-gray-900 sm:text-xl">
            Program Booking Summary
          </h3>
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-4 sm:px-6">
        {isLoading ? (
          <div className="space-y-3">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-center justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-16" />
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="w-full">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <p className="font-helvetica flex-1 text-sm text-red-700">{error}</p>
              <button
                onClick={refetch}
                className="flex items-center gap-1 rounded px-2 py-1 text-xs text-red-600 transition-colors hover:bg-red-100"
                disabled={isLoading}
              >
                <RefreshCw className={`h-3 w-3 ${isLoading ? "animate-spin" : ""}`} />
                Retry
              </button>
            </div>
          </div>
        ) : estimateData ? (
          <div className="space-y-2">
            {/* Base lesson booking */}
            <SummaryLineItem
              label="Program Booking"
              amount={parseFloat(estimateData.booking_price) || 0}
            />

            {/* Tax */}
            {estimateData.tax_amount > 0 && (
              <SummaryLineItem
                label={`Tax (${estimateData.tax_rate}%)`}
                amount={estimateData.tax_amount}
              />
            )}

            {/* Total */}
            <SummaryLineItem
              label="Total Amount"
              amount={parseFloat(estimateData.total)}
              isTotal={true}
              className="border-t border-dashed border-gray-200 pt-2"
            />
          </div>
        ) : (
          <div className="flex items-center justify-center py-4">
            <p className="font-helvetica text-sm text-gray-500">Loading estimate...</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProgramSummarySection;
