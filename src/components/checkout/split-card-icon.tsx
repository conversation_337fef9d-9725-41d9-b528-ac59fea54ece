import React from "react";

const SplitCardIcon = ({ number, color = "#1C5534" }: { number: number; color: string }) => {
  return (
    <div className="relative h-[27.91px] w-8">
      <div data-svg-wrapper className="absolute top-0 left-[9px]">
        <svg
          width="23"
          height="17"
          viewBox="0 0 23 17"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M20.2993 0H2.68595C2.33038 0.0009543 1.97853 0.0720403 1.65069 0.209157C1.32284 0.346274 1.0255 0.546707 0.775804 0.798892C0.526113 1.05108 0.329019 1.35002 0.195893 1.67848C0.0627668 2.00694 -0.00375571 2.35841 0.000163636 2.71261V14.317C0.00016093 15.0273 0.28289 15.7086 0.78636 16.2115C1.28983 16.7145 1.97295 16.998 2.68595 17H20.2993C21.0143 17 21.7001 16.7176 22.2063 16.2146C22.7126 15.7116 22.998 15.0292 23 14.317V2.71261C23.0019 2.3577 22.9336 2.0059 22.7988 1.67736C22.6641 1.34882 22.4656 1.05 22.2147 0.79801C21.9638 0.546023 21.6655 0.345826 21.3368 0.208889C21.0082 0.0719523 20.6556 0.00096702 20.2993 0ZM2.68595 0.73913H20.2993C20.5584 0.739114 20.815 0.790311 21.0541 0.889757C21.2932 0.989203 21.5101 1.13492 21.6923 1.31847C21.8744 1.50202 22.0183 1.71976 22.1154 1.95907C22.2125 2.19837 22.261 2.4545 22.258 2.71261V4.09478H0.742093V2.71261C0.738165 2.45547 0.78549 2.20012 0.881326 1.96133C0.977162 1.72255 1.1196 1.50508 1.3004 1.32153C1.4812 1.13798 1.69676 0.991985 1.93459 0.892014C2.17243 0.792043 2.42781 0.740078 2.68595 0.73913ZM22.258 7.02913H0.742093V4.81174H22.258V7.02913ZM20.2993 16.2609H2.68595C2.43005 16.2609 2.17667 16.2105 1.94035 16.1128C1.70402 16.015 1.48941 15.8717 1.3088 15.6911C1.1282 15.5105 0.985175 15.2961 0.887925 15.0603C0.790674 14.8245 0.741116 14.5719 0.742093 14.317V7.76826H22.258V14.317C22.2561 14.8332 22.0488 15.3276 21.6817 15.692C21.3146 16.0563 20.8175 16.2609 20.2993 16.2609Z"
            fill="black"
          />
        </svg>
      </div>
      <div data-svg-wrapper className="absolute top-[9px] left-[21px]">
        <svg width="8" height="6" viewBox="0 0 8 6" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M5.39954 0C4.90354 0.00336455 4.41831 0.166457 3.99894 0.470756C3.58189 0.16757 3.09932 0.00450455 2.6058 0C2.20159 0.000301894 1.80297 0.108564 1.44143 0.316241C1.07989 0.523917 0.765323 0.825318 0.522574 1.19664C0.279825 1.56797 0.115539 1.99905 0.0426932 2.45584C-0.0301529 2.91263 -0.00956575 3.38262 0.102829 3.8287C0.215223 4.27479 0.416347 4.68474 0.690318 5.02619C0.964288 5.36764 1.3036 5.63123 1.68147 5.79616C2.05933 5.96108 2.46539 6.02282 2.86759 5.9765C3.26978 5.93018 3.6571 5.77706 3.99894 5.52924C4.33913 5.77924 4.72532 5.93476 5.12691 5.98348C5.5285 6.0322 5.93445 5.97279 6.31258 5.80994C6.69071 5.64709 7.03062 5.38529 7.30538 5.04529C7.58014 4.70529 7.78219 4.29643 7.89552 3.85113C8.00884 3.40582 8.03033 2.93631 7.95827 2.47981C7.88621 2.02331 7.72259 1.59236 7.48038 1.22114C7.23817 0.849908 6.92402 0.548602 6.56284 0.341099C6.20165 0.133597 5.80336 0.0256023 5.39954 0.0256766V0ZM0.773104 2.96148C0.773104 2.39397 0.96933 1.84971 1.31862 1.44842C1.6679 1.04713 2.14163 0.821683 2.6356 0.821683C2.88102 0.818237 3.12455 0.871231 3.35188 0.977546C3.5792 1.08386 3.78572 1.24135 3.95927 1.44074C4.13282 1.64013 4.26989 1.87739 4.36243 2.13856C4.45497 2.39973 4.50109 2.67953 4.49809 2.96148C4.49926 3.29391 4.43245 3.62201 4.30305 3.91934C4.17365 4.21668 3.98527 4.47494 3.7531 4.67332C3.47715 4.91514 3.14769 5.06316 2.80216 5.10058C2.45664 5.138 2.1089 5.0633 1.79849 4.88498C1.48807 4.70667 1.22742 4.43188 1.04616 4.09185C0.864894 3.75183 0.770293 3.3602 0.773104 2.96148ZM5.42934 5.10128C5.15064 5.09946 4.87579 5.02636 4.62474 4.8873V4.84451L4.70669 4.70756C4.78561 4.58922 4.85541 4.46321 4.91529 4.33096C4.91529 4.27104 4.96744 4.21113 4.98979 4.14265C5.03728 4.01468 5.07711 3.88314 5.10899 3.74893L5.15369 3.56919C5.20197 3.37078 5.23193 3.16709 5.24309 2.96148C5.24249 2.75703 5.2225 2.55326 5.18349 2.35378L5.13879 2.17404C5.1058 2.03463 5.066 1.89747 5.01959 1.7632L4.94509 1.59201C4.88466 1.45014 4.8123 1.31541 4.72904 1.18973L4.65454 1.0699V1.01854C4.90752 0.887999 5.18182 0.820889 5.45914 0.821683C5.95311 0.821683 6.42684 1.04713 6.77612 1.44842C7.12541 1.84971 7.32164 2.39397 7.32164 2.96148C7.32164 3.52899 7.12541 4.07326 6.77612 4.47455C6.42684 4.87584 5.95311 5.10128 5.45914 5.10128H5.42934Z"
            fill="black"
          />
        </svg>
      </div>
      <div data-svg-wrapper className="absolute top-[10.83px] left-0">
        <svg
          width="18"
          height="18"
          viewBox="0 0 18 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle cx="8.53846" cy="9.36854" r="8.53846" fill={color} />
        </svg>
      </div>
      <div className="absolute top-[7.5px] left-[1.5px] h-[20.74px] w-[14.64px] justify-start text-center text-[10px] leading-[25px] font-semibold text-white">
        X{number}
      </div>
    </div>
  );
};

export default SplitCardIcon;
