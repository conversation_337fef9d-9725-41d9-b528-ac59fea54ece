"use client";
import { CheckoutPayload, processCheckout } from "@/api/booking-checkout-service";
import { createCourtBooking } from "@/api/booking-service";
import { extractErrorMessage } from "@/libs/utils";
import { useBookingStore } from "@/store/booking-store";
import { useCheckoutStore } from "@/store/checkout-store";
import { ClockFadingIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import ValidationError from "../forms/validation-error";
import Loader from "../loader";
import PrimaryLinkButton from "../primary-link-button";
import { StripeProvider } from "../providers/stripe-provider";
import AddonExtrasSelector from "./addon-extras-selector";
import ApplePayButton from "./apple-pay-button";
import PaySplitSection from "./pay-split-section";
import PaymentMethodSelector from "./payment-method-selector";
import SummarySection from "./summary-section";
import { useBookingEstimate, useValidateRedeem } from "@/hooks/use-booking-estimate";

const CourtCheckout = () => {
  const [selectedPaymentMethodId, setSelectedPaymentMethodId] = useState<string>("");
  const [paymentMethodError, setPaymentMethodError] = useState<string>("");
  const [useWallet, setUseWallet] = useState<boolean>(false);
  const [isBooking, setIsBooking] = useState(false);

  const router = useRouter();

  // Get booking details from booking store
  const { courtBookingDetails, clearCourtBooking, setCourtBookingDetails } = useBookingStore();

  // Checkout store
  const {
    checkoutData,
    isProcessing,
    error: checkoutError,
    setCheckoutData,
    updatePaymentMethod,
    updateWalletUsage,
    setProcessing,
    setError,
    setCompleted,
    getCheckoutPayload,
    selectedCourtAttributes,
    totalAmount,
    clearCheckout,
    setTotalAmount,
    setLastTimetoPay,
  } = useCheckoutStore();

  // Prepare estimate request data
  const estimateRequestData = useMemo(() => {
    if (!courtBookingDetails) return null;

    return {
      court_id: courtBookingDetails.court_id,
      sub_court_id: courtBookingDetails.sub_court_id,
      start_time: courtBookingDetails.start_time,
      end_time: courtBookingDetails.end_time,
      split: courtBookingDetails.split,
      attributes: selectedCourtAttributes.length > 0 ? selectedCourtAttributes : undefined,
      guest_users: courtBookingDetails.guest_users?.length
        ? courtBookingDetails.guest_users.map((id) => ({ id }))
        : undefined,
      redeem: courtBookingDetails.redeem,
    };
  }, [courtBookingDetails, selectedCourtAttributes]);

  const validateRedeemRequestData = useMemo(() => {
    if (!courtBookingDetails) return null;

    return {
      court_id: courtBookingDetails.court_id,
      start_time: courtBookingDetails.start_time,
      end_time: courtBookingDetails.end_time,
    };
  }, [courtBookingDetails]);

  // Use custom hook for booking estimate
  const { estimateData, isLoading, error, refetch } = useBookingEstimate(estimateRequestData);

  const {
    validateRedeemData,
    isLoading: isValidateRedeemLoading,
    error: validateRedeemError,
    refetch: validateRedeemRefetch,
  } = useValidateRedeem(validateRedeemRequestData, () => {
    setCourtBookingDetails({ redeem: false });
  });

  console.log("courtBookingDetails from store", courtBookingDetails);

  // Initialize checkout data when booking details are available
  useEffect(() => {
    if (courtBookingDetails && !checkoutData) {
      // Since we don't have a booking_id yet (booking will be created during checkout),
      // we'll use a temporary ID of 0 and update it when the booking is created
      setCheckoutData({
        booking_id: 0, // Temporary - will be updated when booking is created
        payment_method: "CARD", // Default to CARD
        payment_method_id: 0, // Will be set by PaymentMethodSelector
        use_wallet: 0,
      });
    }
  }, [courtBookingDetails, checkoutData, setCheckoutData]);

  // Handler for payment method change
  const handlePaymentMethodChange = useCallback(
    (paymentMethodId: string) => {
      console.log("Payment method changed to:", paymentMethodId);

      if (paymentMethodId === "APPLE_PAY") {
        updatePaymentMethod("APPLE_PAY", 0);
        setSelectedPaymentMethodId(paymentMethodId);
      } else {
        setSelectedPaymentMethodId(paymentMethodId);
        updatePaymentMethod("CARD", Number(paymentMethodId));
      }

      // Clear payment method error
      if (paymentMethodError) {
        setPaymentMethodError("");
      }
    },
    [paymentMethodError, updatePaymentMethod]
  );

  // Handler for wallet toggle
  const handleWalletToggle = useCallback(
    (checked: boolean) => {
      setUseWallet(checked);
      updateWalletUsage(checked);
    },
    [updateWalletUsage]
  );

  // Handler for checkout process
  const handleCheckout = useCallback(async () => {
    try {
      setIsBooking(true);
      if (!checkoutData) {
        setError("Checkout data not initialized");
        return;
      }

      if (!selectedPaymentMethodId) {
        setPaymentMethodError("Please select a payment method");
        return;
      }

      setProcessing(true);
      setError(null);
      setPaymentMethodError("");

      const payload = getCheckoutPayload();
      if (!payload) {
        throw new Error("Invalid checkout data");
      }

      console.log("Processing checkout with payload:", payload);

      if (!courtBookingDetails) {
        throw new Error("Booking details not available");
      }

      const bookingPayload = {
        court_id: courtBookingDetails.court_id,
        sub_court_id: courtBookingDetails.sub_court_id,
        start_time: courtBookingDetails.start_time,
        end_time: courtBookingDetails.end_time,
        split: courtBookingDetails.split,
        redeem: courtBookingDetails.redeem,
        attributes: selectedCourtAttributes,
        guest_users: courtBookingDetails.guest_users,
        booking_price_id: courtBookingDetails.booking_price_id,
        date: courtBookingDetails.date,
        time: courtBookingDetails.time,
        duration: courtBookingDetails.duration,
        price: courtBookingDetails.price,
        is_peak_time: courtBookingDetails.is_peak_time,
      };

      console.log("booking payload:", bookingPayload);

      console.log("checkout payload", payload);

      const bookingResponse = await createCourtBooking(bookingPayload);

      if (bookingResponse) {
        console.log("bookingResponse", bookingResponse.data);
        const bookingId = bookingResponse.data?.data?.id;
        const checkoutPayload = {
          ...payload,
          booking_id: bookingId,
        };
        const checkoutResponse = await processCheckout(checkoutPayload);
        console.log("checkoutResponse", checkoutResponse);
        if (checkoutResponse) {
          setCompleted(true);
          clearCheckout();
          clearCourtBooking();
          // setIsCheckoutCompleted(true);
          router.push(`/booking-success`);
          toast.success("Court Booking Payment processed successfully!");
        } else {
          throw new Error("Checkout failed");
        }
      }
    } catch (error: any) {
      console.error("Checkout error:", error);
      const errorMessage = extractErrorMessage(error) || "Failed to process payment";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsBooking(false);
    }
  }, [
    checkoutData,
    selectedPaymentMethodId,
    setProcessing,
    setError,
    setCompleted,
    getCheckoutPayload,
    selectedCourtAttributes,
    courtBookingDetails,
    clearCheckout,
    clearCourtBooking,
    router,
  ]);

  const handleApplePaySuccess = useCallback(async () => {
    setCompleted(true);
    clearCheckout();
    clearCourtBooking();
    // setIsCheckoutCompleted(true);
    router.push(`/booking-success`);
    console.log("Apple Pay success");
  }, []);

  const handleApplePayBeforePayment = useCallback(async () => {
    try {
      if (!checkoutData) {
        setError("Checkout data not initialized");
        return null;
      }

      if (!selectedPaymentMethodId) {
        setPaymentMethodError("Please select a payment method");
        return null;
      }

      if (selectedPaymentMethodId !== "APPLE_PAY") {
        return null;
      }

      if (!courtBookingDetails) {
        throw new Error("Booking details not available");
      }

      const payload = getCheckoutPayload();
      if (!payload) {
        throw new Error("Invalid checkout data");
      }

      const bookingPayload = {
        court_id: courtBookingDetails.court_id,
        sub_court_id: courtBookingDetails.sub_court_id,
        start_time: courtBookingDetails.start_time,
        end_time: courtBookingDetails.end_time,
        split: courtBookingDetails.split,

        attributes: selectedCourtAttributes,
        guest_users: courtBookingDetails.guest_users,
        booking_price_id: courtBookingDetails.booking_price_id,
        date: courtBookingDetails.date,
        time: courtBookingDetails.time,
        duration: courtBookingDetails.duration,
        price: courtBookingDetails.price,
        is_peak_time: courtBookingDetails.is_peak_time,
      };

      console.log("booking payload:", bookingPayload);

      console.log("checkout payload", payload);

      const bookingResponse = await createCourtBooking(bookingPayload);

      if (bookingResponse) {
        console.log("bookingResponse", bookingResponse.data);
        const bookingId = bookingResponse.data?.data?.id;

        const checkoutPayload: CheckoutPayload = {
          payment_method: "APPLE_PAY",
          booking_id: bookingId,
          use_wallet: checkoutData.use_wallet,
        };
        const checkoutResponse = await processCheckout(checkoutPayload);
        console.log("apple checkoutResponse", checkoutResponse);
        if (checkoutResponse) {
          return {
            bookingId: bookingId,
            clientSecret: checkoutResponse.data?.data?.apple_payment_intent?.client_secret,
          };
        } else {
          throw new Error("Checkout failed");
        }
      }
      return null;
    } catch (error) {
      console.error("Apple Pay before payment error:", error);
      setError(extractErrorMessage(error) || "Failed to process payment");
      return null;
    }
  }, [checkoutData, selectedPaymentMethodId, setError]);

  const handleRedeemToggle = useCallback(() => {
    setCourtBookingDetails({ redeem: !courtBookingDetails?.redeem });
  }, [courtBookingDetails]);

  if (!courtBookingDetails) {
    return (
      <div className="flex w-full flex-col items-center justify-center gap-4 py-12 text-center">
        <p className="font-helvetica text-lg font-bold text-gray-900">
          No booking details available
        </p>
        <PrimaryLinkButton href="/locations/charlotte" className="w-fit" text="Back to booking" />
      </div>
    );
  }

  return (
    <div className="w-full px-1 py-1 lg:px-4">
      <div className="w-full">
        <div className="flex w-full flex-col items-start justify-start rounded-[30px] bg-white px-2 pt-2 pb-6 lg:flex-row lg:items-start lg:p-10">
          {/* <button
            onClick={() => router.push("/locations/charlotte?booking_type=court")}
            className="hover:text-primary absolute top-4 left-1 z-10 flex h-12 w-12 items-center justify-center text-gray-600 transition-colors duration-200 lg:left-4"
          >
            <ChevronLeft className="h-8 w-8" />
          </button> */}
          <div className="flex w-full flex-col items-start justify-start gap-3 px-4 lg:w-1/2">
            <div className="font-helvetica flex w-full items-center justify-between gap-1 border-b border-gray-300 pb-4 text-xl font-bold text-black">
              <div>
                <p>Court Booking</p>
                <p>{courtBookingDetails.court_name || "Court"}</p>
                <p>{courtBookingDetails.date}</p>
                <p>{courtBookingDetails.time}</p>
              </div>
              <div className="flex flex-col items-center justify-center gap-2">
                <ClockFadingIcon className="text-primary" />
                <div className="justify-center">
                  <span className="font-helvetica text-xl leading-[25px] font-bold text-black">
                    {courtBookingDetails.duration}
                  </span>
                </div>
              </div>
            </div>
            <PaySplitSection
              estimateData={estimateData}
              isLoading={isLoading}
              error={error}
              refetch={refetch}
            />
            <div className="my-1 h-px w-full bg-gray-300"></div>
            {/* Addon extras selector - temporarily disabled since we don't have bookingId yet */}
            <AddonExtrasSelector />
            {!validateRedeemError &&
              !isValidateRedeemLoading &&
              validateRedeemData &&
              validateRedeemData.status === "success" && (
                <div className="mb-6 flex w-full items-center justify-between rounded-[20px] bg-[#fffaed] px-[25px] py-[14px] outline-1 outline-[#ddba0a] transition-colors outline-dashed hover:bg-[#fff8e1]">
                  <p className="text-primary">Redeem Free Booking</p>
                  <button onClick={handleRedeemToggle} className="text-primary">
                    {courtBookingDetails?.redeem ? "Remove" : "Apply"}
                  </button>
                </div>
              )}
          </div>
          <div className="z-1 hidden h-full w-[2px] rounded-full bg-[#EBEBEB] text-gray-300 lg:block"></div>
          <div className="flex w-full flex-col items-start justify-between px-4 lg:w-1/2">
            {/* Payment method selector */}
            <PaymentMethodSelector
              selectedPaymentMethodId={selectedPaymentMethodId}
              onPaymentMethodChange={handlePaymentMethodChange}
              error={paymentMethodError}
              disabled={isProcessing}
              useWallet={useWallet}
              onWalletToggle={handleWalletToggle}
            />

            <SummarySection
              estimateData={estimateData}
              isLoading={isLoading}
              error={error}
              refetch={refetch}
            />

            {/* Checkout Error */}
            {checkoutError && (
              <div className="mt-4 flex w-full justify-center">
                <ValidationError id="checkout-error" error={checkoutError} />
              </div>
            )}

            {/* Checkout Button */}
            {selectedPaymentMethodId === "APPLE_PAY" ? (
              <StripeProvider
                options={{
                  mode: "payment",
                  amount: Math.round(totalAmount * 100),
                  currency: "usd",
                }}
              >
                <div className="mt-6 flex w-full flex-col">
                  <ApplePayButton
                    onBeforePayment={handleApplePayBeforePayment}
                    amount={Math.round(totalAmount * 100)} // Convert to cents
                    onPaymentSuccess={handleApplePaySuccess}
                    disabled={
                      isProcessing ||
                      !selectedPaymentMethodId ||
                      !checkoutData ||
                      checkoutError !== null
                    }
                  />
                </div>
              </StripeProvider>
            ) : (
              <div className="mt-6 w-full">
                <button
                  type="button"
                  onClick={handleCheckout}
                  disabled={
                    isProcessing ||
                    !selectedPaymentMethodId ||
                    !checkoutData ||
                    checkoutError !== null
                  }
                  className="bg-primary hover:bg-primary/90 font-helvetica w-full rounded-full px-4 py-3 font-bold text-white transition-colors disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {isProcessing || isBooking ? <Loader /> : "Complete Payment"}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourtCheckout;
