"use client";

import React, { useEffect, useState } from "react";
import { Skeleton } from "../ui/skeleton";
import { createSetupIntent } from "@/api/payment-service"; // Assume this creates a PaymentIntent

interface ApplePayButtonProps {
  amount: number; // Amount in minor currency units (e.g., cents)
  onPaymentSuccess: (txn_ref: string) => void; // Callback to pass txn_ref to parent
}

declare global {
  interface Window {
    ApplePaySession: any;
  }
}

const ApplePayButtonV2: React.FC<ApplePayButtonProps> = ({ amount, onPaymentSuccess }) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isAvailable, setIsAvailable] = useState<boolean>(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [paymentIntentId, setPaymentIntentId] = useState<string | null>(null);

  // Fetch client secret and PaymentIntent ID on component mount
  useEffect(() => {
    const fetchClientSecret = async () => {
      try {
        setIsLoading(true);
        const response = await createSetupIntent(); // Assume this returns PaymentIntent
        const secret = response?.data?.data?.client_secret;
        const intentId = response?.data?.data?.id; // PaymentIntent ID (e.g., pi_3Py66ZAdo4FlL1xn0RSmJX8G)
        if (secret && intentId) {
          setClientSecret(secret);
          setPaymentIntentId(intentId);
        }
      } catch (error) {
        console.error("Error fetching PaymentIntent:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchClientSecret();
  }, []);

  // Check if Apple Pay is available
  useEffect(() => {
    if (typeof window.ApplePaySession === "undefined") {
      setIsAvailable(false);
      setIsLoading(false);
      return;
    }

    // Check if Apple Pay is supported
    if (window.ApplePaySession && window.ApplePaySession.canMakePayments()) {
      setIsAvailable(true);
    } else {
      setIsAvailable(false);
    }
    setIsLoading(false);
  }, []);

  // Handle Apple Pay button click
  const handleApplePayClick = async () => {
    if (!window.ApplePaySession) {
      console.error("Apple Pay or PaymentIntent not available");
      return;
    }

    // Create Apple Pay payment request
    const paymentRequest = {
      countryCode: "US",
      currencyCode: "USD",
      supportedNetworks: ["visa", "masterCard", "amex", "discover"],
      merchantCapabilities: ["supports3DS"],
      total: {
        label: "Epic Padel",
        amount: (amount / 100).toFixed(2), // Convert cents to dollars
      },
      requiredBillingContactFields: ["postalAddress", "name"],
      requiredShippingContactFields: ["email", "name"],
    };

    // Start Apple Pay session
    const session = new window.ApplePaySession(6, paymentRequest);

    // Handle merchant validation
    session.onvalidatemerchant = async (event: any) => {
      try {
        console.log("Merchant validation requested for:", event.validationURL);

        // Call your backend to validate the merchant
        const response = await fetch("/api/apple-pay/validate-merchant", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            validationURL: event.validationURL,
            displayName: "Epic Padel",
          }),
        });

        const result = await response.json();

        if (result.error) {
          console.error("Merchant validation failed:", result.error);
          session.completeMerchantValidation(window.ApplePaySession.STATUS_FAILURE);
        } else {
          console.log("Merchant session received:", result);
          session.completeMerchantValidation(result);
        }
      } catch (error) {
        console.error("Merchant validation error:", error);
        session.completeMerchantValidation(window.ApplePaySession.STATUS_FAILURE);
      }
    };

    // Handle payment authorization
    session.onpaymentauthorized = async (event: any) => {
      try {
        console.log("Payment authorized, processing...");
        const paymentToken = event.payment.token;

        console.log("paymentToken", paymentToken);
        console.log("paymentIntentId", paymentIntentId);

        // // Send payment token to your backend to confirm the PaymentIntent
        // const response = await fetch("/api/apple-pay/confirm-payment", {
        //   method: "POST",
        //   headers: { "Content-Type": "application/json" },
        //   body: JSON.stringify({
        //     paymentIntentId,
        //     paymentToken,
        //     clientSecret,
        //   }),
        // });

        // const result = await response.json();

        // if (result.error) {
        //   console.error("Payment confirmation failed:", result.error);
        //   session.completePayment(window.ApplePaySession.STATUS_FAILURE);
        // } else {
        //   console.log("Payment successful:", result);
        //   session.completePayment(window.ApplePaySession.STATUS_SUCCESS);
        //   onPaymentSuccess(result.txn_ref || paymentIntentId); // Pass txn_ref to parent
        // }
      } catch (error) {
        console.error("Payment processing error:", error);
        session.completePayment(window.ApplePaySession.STATUS_FAILURE);
      }
    };

    // Handle session cancellation
    session.oncancel = () => {
      console.log("Apple Pay session cancelled");
    };

    // Begin the Apple Pay session
    session.begin();
  };

  // Show skeleton while loading
  if (isLoading) {
    return <Skeleton className="h-12 w-full rounded-full" />;
  }

  // Show message if Apple Pay is not available
  if (!isAvailable) {
    return (
      <div className="text-center text-sm text-gray-500">
        Apple Pay is not available on this device/browser.
      </div>
    );
  }

  // Render custom Apple Pay button
  return (
    <button
      onClick={handleApplePayClick}
      className="flex h-12 w-full items-center justify-center rounded-full bg-black text-white"
      style={{ WebkitAppearance: "-apple-pay-button" }}
    ></button>
  );
};

export default ApplePayButtonV2;
