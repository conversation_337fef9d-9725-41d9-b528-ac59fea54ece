import { useGetPaymentMethods } from "@/api/payment-service";
import { getCardBrandIcon } from "@/libs/utils";
import * as Dialog from "@radix-ui/react-dialog";
import { Plus, X, ChevronDown, ChevronUp } from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import ValidationError from "../forms/validation-error";
import { AddPaymentCardForm } from "../payments/add-payment-card-form";
import { StripeProvider } from "../providers/stripe-provider";
import { Skeleton } from "../ui/skeleton";
import { Switch } from "../ui/switch";
import Image from "next/image";
import { useAuthStore } from "@/store/auth-store";
import { useCheckoutStore } from "@/store/checkout-store";

// Payment method interface based on API usage
interface PaymentMethod {
  id: string;
  brand: string;
  last_number: string;
  is_default: boolean;
}

interface PaymentMethodSelectorProps {
  selectedPaymentMethodId: string;
  onPaymentMethodChange: (paymentMethodId: string) => void;
  error?: string;
  disabled?: boolean;
  useWallet: boolean;
  onWalletToggle: (checked: boolean) => void;
}

const PaymentMethodSelector = ({
  selectedPaymentMethodId,
  onPaymentMethodChange,
  error,
  disabled = false,
  useWallet,
  onWalletToggle,
}: PaymentMethodSelectorProps) => {
  const [showPaymentDialog, setShowPaymentDialog] = useState<boolean>(false);
  const [showAllPaymentMethods, setShowAllPaymentMethods] = useState<boolean>(false);
  const { user } = useAuthStore();
  const { setError: setCheckoutError, totalAmount } = useCheckoutStore();
  const WALLET_TOTAL_AMOUT_DIFFERENCE = 2;

  const userWalletAmount = useMemo(() => {
    return user?.wallet_amount ? parseFloat(user.wallet_amount).toFixed(2) : null;
  }, [user?.wallet_amount]);

  const { paymentMethodList, paymentMethodLoading } = useGetPaymentMethods();

  // Memoized typed payment methods list
  const typedPaymentMethods = useMemo((): PaymentMethod[] => {
    return (paymentMethodList || []) as PaymentMethod[];
  }, [paymentMethodList]);

  // Memoized default payment method
  const defaultPaymentMethod = useMemo(() => {
    return typedPaymentMethods.find((method) => method.is_default);
  }, [typedPaymentMethods]);

  // Memoized payment methods to display based on show all state
  const displayedPaymentMethods = useMemo(() => {
    if (typedPaymentMethods.length <= 2 || showAllPaymentMethods) {
      return typedPaymentMethods;
    }
    return typedPaymentMethods.slice(0, 2);
  }, [typedPaymentMethods, showAllPaymentMethods]);

  // Check if we need to show the toggle button
  const shouldShowToggle = useMemo(() => {
    return typedPaymentMethods.length > 2;
  }, [typedPaymentMethods.length]);

  // Effect to set default payment method when payment methods are loaded
  useEffect(() => {
    if (defaultPaymentMethod && !selectedPaymentMethodId) {
      onPaymentMethodChange(String(defaultPaymentMethod.id));
    }
  }, [defaultPaymentMethod, selectedPaymentMethodId, onPaymentMethodChange]);

  // Handler for payment method change
  const handlePaymentMethodChange = useCallback(
    (paymentMethodId: string) => {
      if (!disabled) {
        onPaymentMethodChange(paymentMethodId);
      }
    },
    [disabled, onPaymentMethodChange]
  );

  // Handler for showing payment form
  const handleShowPaymentForm = useCallback(() => {
    setShowPaymentDialog(true);
  }, []);

  // Handler for closing payment dialog
  const handleClosePaymentDialog = useCallback((open: boolean) => {
    setShowPaymentDialog(open);
  }, []);

  // Handler for wallet toggle
  const handleWalletToggle = useCallback(
    (checked: boolean) => {
      if (!disabled) {
        onWalletToggle(checked);
        if (
          checked &&
          totalAmount - parseFloat(userWalletAmount!) <= WALLET_TOTAL_AMOUT_DIFFERENCE
        ) {
          setCheckoutError(
            "We cannot process payments less than $2.00. Please add more funds to your wallet either use only card."
          );
        } else {
          setCheckoutError(null);
        }
      }
    },
    [disabled, onWalletToggle]
  );

  // Handler for toggling payment methods visibility
  const handleTogglePaymentMethods = useCallback(() => {
    setShowAllPaymentMethods((prev) => !prev);
  }, []);

  return (
    <StripeProvider>
      <div className="w-full space-y-4">
        <div className="space-y-2">
          <label className="block text-lg font-medium text-gray-900">Payment Method</label>

          <div
            className={`relative cursor-pointer p-4 transition-all duration-200 ${disabled ? "cursor-not-allowed opacity-50" : ""}`}
            onClick={() => handlePaymentMethodChange("APPLE_PAY")}
            role="button"
            tabIndex={disabled ? -1 : 0}
            onKeyDown={(e) => {
              if (!disabled && (e.key === "Enter" || e.key === " ")) {
                e.preventDefault();
                handlePaymentMethodChange("APPLE_PAY");
              }
            }}
            aria-pressed={selectedPaymentMethodId === "APPLE_PAY"}
            aria-describedby={error ? "payment-error" : undefined}
            aria-disabled={disabled}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {/* Selection Indicator */}
                <div className="flex items-center justify-center">
                  <div data-svg-wrapper>
                    <svg
                      width="25"
                      height="25"
                      viewBox="0 0 25 25"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect
                        width="25"
                        height="25"
                        rx="5"
                        fill={selectedPaymentMethodId === "APPLE_PAY" ? "#1C5534" : "#F5F5F5"}
                      />
                      {selectedPaymentMethodId === "APPLE_PAY" && (
                        <path
                          d="M10.7563 17.2439C10.1798 17.8599 9.24427 17.8599 8.66799 17.2439L5.43241 13.7872C4.85586 13.1716 4.85586 12.1721 5.43241 11.5565C6.00868 10.9405 6.94419 10.9405 7.52074 11.5565L9.44851 13.6157C9.59404 13.7709 9.83028 13.7709 9.97608 13.6157L15.1959 8.03911C15.7722 7.42316 16.7077 7.42316 17.2842 8.03911C17.5611 8.3349 17.7167 8.73622 17.7167 9.15449C17.7167 9.57275 17.5611 9.97408 17.2842 10.2699L10.7563 17.2439Z"
                          fill="white"
                        />
                      )}
                    </svg>
                  </div>
                </div>

                {/* Apple Pay Label */}
                <div className="flex flex-col">
                  <span className="font-medium text-gray-900">Apple Pay</span>
                </div>
              </div>

              {/* Apple Pay Icon */}
              <div className="flex items-center gap-2">
                <Image
                  src="/imgs/apple_pay.svg"
                  alt="Apple Pay"
                  width={40}
                  height={40}
                  className="object-contain"
                />
              </div>
            </div>
          </div>

          {paymentMethodLoading ? (
            <div className="space-y-3">
              <Skeleton className="h-16 w-full rounded-lg" />
              <Skeleton className="h-16 w-full rounded-lg" />
            </div>
          ) : (
            <div className="space-y-1">
              {!typedPaymentMethods.length ? (
                <div className="flex items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-8 text-gray-500">
                  <span>No payment methods available</span>
                </div>
              ) : (
                <>
                  {displayedPaymentMethods.map((method) => (
                    <div
                      key={method.id}
                      className={`relative cursor-pointer border-t-1 border-gray-300 p-4 transition-all duration-200 ${
                        disabled ? "cursor-not-allowed opacity-50" : ""
                      }`}
                      onClick={() => handlePaymentMethodChange(String(method.id))}
                      role="button"
                      tabIndex={disabled ? -1 : 0}
                      onKeyDown={(e) => {
                        if (!disabled && (e.key === "Enter" || e.key === " ")) {
                          e.preventDefault();
                          handlePaymentMethodChange(String(method.id));
                        }
                      }}
                      aria-pressed={selectedPaymentMethodId === String(method.id)}
                      aria-describedby={error ? "payment-error" : undefined}
                      aria-disabled={disabled}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          {/* Selection Indicator */}
                          <div className="flex items-center justify-center">
                            <div data-svg-wrapper>
                              <svg
                                width="25"
                                height="25"
                                viewBox="0 0 25 25"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <rect
                                  width="25"
                                  height="25"
                                  rx="5"
                                  fill={
                                    selectedPaymentMethodId === String(method.id)
                                      ? "#1C5534"
                                      : "#F5F5F5"
                                  }
                                />
                                {selectedPaymentMethodId === String(method.id) && (
                                  <path
                                    d="M10.7563 17.2439C10.1798 17.8599 9.24427 17.8599 8.66799 17.2439L5.43241 13.7872C4.85586 13.1716 4.85586 12.1721 5.43241 11.5565C6.00868 10.9405 6.94419 10.9405 7.52074 11.5565L9.44851 13.6157C9.59404 13.7709 9.83028 13.7709 9.97608 13.6157L15.1959 8.03911C15.7722 7.42316 16.7077 7.42316 17.2842 8.03911C17.5611 8.3349 17.7167 8.73622 17.7167 9.15449C17.7167 9.57275 17.5611 9.97408 17.2842 10.2699L10.7563 17.2439Z"
                                    fill="white"
                                  />
                                )}
                              </svg>
                            </div>
                          </div>

                          {/* Card Details */}
                          <div className="flex flex-col">
                            <span className="font-medium text-gray-900">
                              {method.brand?.toUpperCase()} •••• {method.last_number}
                            </span>
                            {method.is_default && (
                              <span className="text-primary text-xs font-medium">Default</span>
                            )}
                          </div>
                        </div>
                        <div className="flex h-8 w-12 items-center justify-center rounded">
                          {method.brand?.toLowerCase() && (
                            <img
                              src={getCardBrandIcon(method.brand)}
                              alt={`${method.brand} logo`}
                              className="h-6 w-auto"
                            />
                          )}
                          {!["visa", "mastercard"].includes(method.brand?.toLowerCase() || "") && (
                            <span className="text-xs font-semibold text-gray-600">
                              {method.brand?.toUpperCase().slice(0, 4)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Show more/less toggle button */}
                  {shouldShowToggle && (
                    <div className="flex items-center justify-center pt-2">
                      <button
                        type="button"
                        onClick={handleTogglePaymentMethods}
                        className="text-primary hover:text-primary/80 flex items-center gap-2 text-sm transition-colors duration-200"
                        disabled={disabled}
                      >
                        <span>{showAllPaymentMethods ? "Show less" : "Show others"}</span>
                        {showAllPaymentMethods ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                  )}
                </>
              )}
            </div>
          )}

          {error && <ValidationError id="payment-error" error={error} />}

          {/* Wallet Usage Toggle */}
          <div className="">
            <div
              className={`flex items-center justify-between rounded-lg p-4 transition-all duration-200 ${disabled ? "cursor-not-allowed opacity-50" : ""}`}
            >
              <div className="flex items-center gap-2 space-y-1">
                <Switch
                  checked={useWallet}
                  onCheckedChange={handleWalletToggle}
                  disabled={disabled || !userWalletAmount || parseFloat(userWalletAmount) === 0}
                  aria-label="Use wallet balance"
                  className="data-[state=checked]:bg-primary data-[state=unchecked]:bg-gray-300"
                />
                <div className="flex flex-col items-start justify-center">
                  <p className="text-sm font-medium text-gray-900">Use Wallet Balance</p>
                  {/* Placeholder for wallet balance - can be enhanced when API is available */}
                  <p className="text-xs text-gray-400">
                    Available balance: ${userWalletAmount || "N/A"}
                  </p>
                </div>
              </div>

              <Image
                src="/imgs/checkout/wallet.png"
                alt="Wallet Icon"
                width={25}
                height={25}
                className="object-contain"
              />
            </div>
          </div>
          <div className="mb-6 flex w-full items-center justify-center">
            <button
              type="button"
              className="text-primary hover:text-primary flex items-center justify-center gap-2 disabled:cursor-not-allowed disabled:opacity-50"
              onClick={handleShowPaymentForm}
              disabled={paymentMethodLoading || disabled}
            >
              <Plus className="size-6" /> <span>Add New Card</span>
            </button>
          </div>
        </div>
      </div>

      {/* Add Payment Method Dialog */}
      <Dialog.Root open={showPaymentDialog} onOpenChange={handleClosePaymentDialog}>
        <Dialog.Portal>
          <Dialog.Overlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[2000] bg-black/50" />
          <Dialog.Content className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-[2001] w-[96%] max-w-lg translate-x-[-50%] translate-y-[-50%] rounded-[20px] bg-white p-5 shadow-lg duration-200 lg:w-full">
            <div className="flex items-center justify-between">
              <Dialog.Title className="text-lg font-semibold">Add Payment Method</Dialog.Title>
              <Dialog.Close className="focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-2 right-2 rounded-full opacity-70 ring-0 transition-opacity hover:opacity-100 focus:ring-0 focus:outline-none disabled:pointer-events-none">
                <X className="h-8 w-8 text-gray-600 outline-none" />
                <span className="sr-only">Close</span>
              </Dialog.Close>
            </div>

            <AddPaymentCardForm setOpen={handleClosePaymentDialog} />
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>
    </StripeProvider>
  );
};

export default PaymentMethodSelector;
