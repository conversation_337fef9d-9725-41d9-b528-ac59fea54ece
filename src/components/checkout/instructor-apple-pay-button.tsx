"use client";

import {
  applePayCourtBookingConfirm,
  applePayInstructorBookingConfirm,
} from "@/api/booking-checkout-service";
import { PaymentRequestButtonElement, useStripe } from "@stripe/react-stripe-js";
import React, { useEffect, useState } from "react";
import Loader from "../loader";
import { Skeleton } from "../ui/skeleton";

interface ApplePayButtonProps {
  amount: number; // Amount in minor currency units (e.g. cents)
  onBeforePayment: () => Promise<{ bookingId: number; clientSecret: string } | null>;
  onPaymentSuccess: () => void;
}

const InstructorApplePayButton: React.FC<ApplePayButtonProps> = ({
  amount,
  onBeforePayment,
  onPaymentSuccess,
}) => {
  const stripe = useStripe();
  const [paymentRequest, setPaymentRequest] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isAvailable, setIsAvailable] = useState<boolean>(false);
  const [isProcessingPayment, setIsProcessingPayment] = useState<boolean>(false);

  useEffect(() => {
    if (!stripe || !amount) return;

    console.log("Creating payment request...");

    // Reset states when creating new payment request
    setPaymentRequest(null);
    setIsAvailable(false);

    const pr = stripe.paymentRequest({
      country: "US",
      currency: "usd",
      total: {
        label: "Total",
        amount: amount,
      },
      requestPayerName: true,
      requestPayerEmail: true,
    });

    pr.canMakePayment().then((result) => {
      if (result) {
        setPaymentRequest(pr);
        setIsAvailable(true);
      } else {
        setIsAvailable(false);
      }
    });

    pr.on("paymentmethod", async (ev) => {
      try {
        setIsProcessingPayment(true);

        console.log("Calling onBeforePayment to create booking...");
        const bookingDetails = await onBeforePayment();

        if (!bookingDetails) {
          console.error("Failed to create booking - no booking ID returned");
          setIsProcessingPayment(false);
          ev.complete("fail");
          return;
        }

        console.log("Booking created with ID:", bookingDetails.bookingId);

        console.log("client secret id: ", bookingDetails.clientSecret);

        const { error, paymentIntent } = await stripe.confirmCardPayment(
          bookingDetails.clientSecret,
          { payment_method: ev.paymentMethod.id },
          { handleActions: false }
        );

        if (error) {
          console.error("Payment failed:", error);
          setIsProcessingPayment(false);
          ev.complete("fail");
        } else {
          console.log("Payment successful:", paymentIntent.id);

          try {
            console.log("Confirming Apple Pay booking...");
            await applePayInstructorBookingConfirm({
              booking_id: bookingDetails.bookingId,
              txn_ref: paymentIntent.id,
            });

            ev.complete("success");
            setIsProcessingPayment(false);
            onPaymentSuccess();
          } catch (confirmError) {
            console.error("Apple Pay confirmation failed:", confirmError);
            setIsProcessingPayment(false);
            ev.complete("fail");
          }
        }
      } catch (error) {
        console.error("Payment processing error:", error);
        setIsProcessingPayment(false);
        ev.complete("fail");
      }
    });
  }, [stripe, amount, onBeforePayment, onPaymentSuccess]);

  if (isLoading) {
    return <Skeleton className="h-12 w-full rounded-full" />;
  }

  if (!isAvailable || !paymentRequest) {
    return (
      <div className="flex h-[50px] w-full items-center justify-center rounded-full bg-black text-center text-sm text-gray-500">
        Apple Pay is not available on this device/browser.
      </div>
    );
  }

  if (isProcessingPayment) {
    return (
      <div className="flex h-[50px] w-full items-center justify-center rounded-full bg-black text-white">
        <Loader className="text-white" />
      </div>
    );
  }

  return (
    <PaymentRequestButtonElement
      key={`apple-pay-${amount}`} // Force recreation when amount changes
      className="h-[50px] w-full rounded-full bg-black text-white"
      options={{
        paymentRequest: paymentRequest,
        style: {
          paymentRequestButton: {
            type: "default",
            theme: "dark",
            height: "50px",
            borderRadius: "25px",
          } as any,
        },
      }}
    />
  );
};

export default InstructorApplePayButton;
