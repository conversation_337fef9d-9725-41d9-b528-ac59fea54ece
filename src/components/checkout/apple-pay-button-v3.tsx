"use client";

import { applePayCourtBookingConfirm } from "@/api/booking-checkout-service";
import { ExpressCheckoutElement, useStripe } from "@stripe/react-stripe-js";
import React, { useEffect, useState } from "react";
import { Skeleton } from "../ui/skeleton";
import { StripeExpressCheckoutElementOptions } from "@stripe/stripe-js";

interface ApplePayButtonProps {
  amount: number;
  onBeforePayment: () => Promise<{ bookingId: number; clientSecret: string } | null>;
  onPaymentSuccess: () => void;
}

const ApplePayButtonV3: React.FC<ApplePayButtonProps> = ({
  amount,
  onBeforePayment,
  onPaymentSuccess,
}) => {
  const stripe = useStripe();
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [bookingId, setBookingId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const options: StripeExpressCheckoutElementOptions = {
    emailRequired: true,
    buttonType: {
      googlePay: "book",
      applePay: "book",
      paypal: "buynow",
    },
    buttonTheme: {
      applePay: "black",
    },
    buttonHeight: 55,
  };

  const [visibility, setVisibility] = useState("hidden");

  const onReady = ({ availablePaymentMethods }: { availablePaymentMethods: any }) => {
    console.log("availablePaymentMethods", availablePaymentMethods);
    if (!availablePaymentMethods) {
      // No buttons will show
    } else {
      // Optional: Animate in the Element
      setVisibility("initial");
    }
  };

  useEffect(() => {
    if (!stripe || !amount) return;

    const initialize = async () => {
      setIsLoading(true);
      const result = await onBeforePayment();

      if (result) {
        setClientSecret(result.clientSecret);
        setBookingId(result.bookingId);
      }

      setIsLoading(false);
    };

    initialize();
  }, [stripe, amount, onBeforePayment]);

  const handleComplete = async (event: any) => {
    console.log("event", event);
    if (event.type === "payment_successful" && clientSecret && bookingId) {
      try {
        await applePayCourtBookingConfirm({
          booking_id: bookingId,
          txn_ref: event.paymentIntent.id,
        });

        onPaymentSuccess();
      } catch (err) {
        console.error("Error confirming booking:", err);
      }
    }
  };

  if (isLoading) {
    return <Skeleton className="h-12 w-full rounded-full" />;
  }

  if (!clientSecret) {
    return (
      <div className="flex h-[50px] w-full items-center justify-center rounded-full bg-black text-sm text-gray-500">
        Initializing payment...
      </div>
    );
  }

  return (
    <div id="checkout-page">
      <ExpressCheckoutElement
        options={options}
        onConfirm={handleComplete}
        onReady={onReady}
        className="h-[50px] w-full rounded-full bg-black text-white"
      />
    </div>
  );
};

export default ApplePayButtonV3;
