import { confirmProgramCheckout, createProgramBooking } from "@/api/booking-program-service";
import { extractErrorMessage } from "@/libs/utils";
import { useBookingStore } from "@/store/booking-store";
import { useCheckoutStore } from "@/store/checkout-store";
import { useRouter } from "next/navigation";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import Loader from "../loader";
import { StripeProvider } from "../providers/stripe-provider";
import PaymentMethodSelector from "./payment-method-selector";
import ProgramApplePayButton from "./program-apple-pay-button";
import ProgramSummarySection from "./program-summary-section";

const ProgramCheckout = () => {
  const router = useRouter();
  const [selectedPaymentMethodId, setSelectedPaymentMethodId] = useState<string>("");
  const [paymentMethodError, setPaymentMethodError] = useState<string>("");
  const [useWallet, setUseWallet] = useState<boolean>(false);
  const [isBooking, setIsBooking] = useState(false);
  const { programBookingDetails, clearAllBookings } = useBookingStore();

  const {
    updatePaymentMethod,
    updateWalletUsage,
    getCheckoutPayload,
    clearCheckout,
    totalAmount,
    error: checkoutError,
    setCheckoutData,
    setProcessing,
    setError,
    setCompleted,
  } = useCheckoutStore();

  const handleWalletToggle = useCallback(
    (checked: boolean) => {
      setUseWallet(checked);
      updateWalletUsage(checked);
    },
    [updateWalletUsage]
  );

  // Handler for payment method change
  const handlePaymentMethodChange = useCallback(
    (paymentMethodId: string) => {
      console.log("Payment method changed to:", paymentMethodId);

      if (paymentMethodId === "APPLE_PAY") {
        updatePaymentMethod("APPLE_PAY", 0);
        setSelectedPaymentMethodId(paymentMethodId);
      } else {
        updatePaymentMethod("CARD", Number(paymentMethodId));
        setSelectedPaymentMethodId(paymentMethodId);
      }

      // Clear payment method error
      if (paymentMethodError) {
        setPaymentMethodError("");
      }
    },
    [paymentMethodError, updatePaymentMethod]
  );

  const handleCheckout = useCallback(async () => {
    try {
      setIsBooking(true);
      if (!selectedPaymentMethodId) {
        setPaymentMethodError("Please select a payment method");
        return;
      }

      const payload = getCheckoutPayload();
      console.log("checkout payload", payload);
      if (!payload) {
        throw new Error("Invalid checkout data");
      }

      if (!programBookingDetails) {
        throw new Error("Program booking details not available");
      }

      const bookingPayload: any = {
        program_id: programBookingDetails.programId,
        payment_type: programBookingDetails.sessionType,
        use_wallet: payload.use_wallet,
      };

      if (
        programBookingDetails.sessionType === "PER_SESSION" &&
        programBookingDetails.selectedClassIds.length > 0
      ) {
        bookingPayload["program_class_id"] = programBookingDetails.selectedClassIds;
      }

      const bookingResponse = await createProgramBooking(bookingPayload);

      if (bookingResponse) {
        console.log("bookingResponse", bookingResponse.data);
        const bookingId = bookingResponse.data?.data?.id;
        const checkoutPayload = {
          payment_method: payload.payment_method,
          payment_method_id: payload.payment_method_id,
          use_wallet: payload.use_wallet,
          id: bookingId,
        };
        const checkoutResponse = await confirmProgramCheckout(checkoutPayload);
        console.log("checkoutResponse", checkoutResponse);
        if (checkoutResponse) {
          toast.success("Program Booking Payment processed successfully!");
          setIsBooking(false);
          clearAllBookings();
          router.push(`/booking-success`);
        } else {
          throw new Error("Checkout failed");
        }
      }
      setPaymentMethodError("");
    } catch (error) {
      console.error("Checkout error:", error);
      toast.error("Checkout failed");
    } finally {
      setIsBooking(false);
    }
  }, [selectedPaymentMethodId]);

  const handleApplePaySuccess = useCallback(async () => {
    clearCheckout();
    clearAllBookings();
    // setIsCheckoutCompleted(true);
    router.push(`/booking-success`);
    console.log("Apple Pay success");
  }, []);

  const handleApplePayBeforePayment = useCallback(async () => {
    try {
      if (!selectedPaymentMethodId) {
        setPaymentMethodError("Please select a payment method");
        return null;
      }

      if (selectedPaymentMethodId !== "APPLE_PAY") {
        return null;
      }

      if (!programBookingDetails) {
        throw new Error("Booking details not available");
      }

      const payload = getCheckoutPayload();
      if (!payload) {
        throw new Error("Invalid checkout data");
      }

      const bookingPayload: any = {
        program_id: programBookingDetails.programId,
        payment_type: programBookingDetails.sessionType,
        use_wallet: payload.use_wallet,
      };

      if (
        programBookingDetails.sessionType === "PER_SESSION" &&
        programBookingDetails.selectedClassIds.length > 0
      ) {
        bookingPayload["program_class_id"] = programBookingDetails.selectedClassIds;
      }

      const bookingResponse = await createProgramBooking(bookingPayload);

      if (bookingResponse) {
        console.log("bookingResponse", bookingResponse.data);
        const bookingId = bookingResponse.data?.data?.id;

        const checkoutPayload: any = {
          payment_method: "APPLE_PAY",
          id: bookingId,
          use_wallet: payload.use_wallet,
        };
        const checkoutResponse = await confirmProgramCheckout(checkoutPayload);
        console.log("confirm checkoutResponse", checkoutResponse);
        if (checkoutResponse) {
          return {
            bookingId: bookingId,
            clientSecret: checkoutResponse.data?.data?.appleIntent?.client_secret,
          };
        } else {
          throw new Error("Checkout failed");
        }
      }
      return null;
    } catch (error) {
      console.error("Apple Pay before payment error:", error);
      const errorMessage = extractErrorMessage(error) || "Failed to process payment";
      setError(errorMessage);
      toast.error(errorMessage);
      setError(errorMessage);
      return null;
    }
  }, [selectedPaymentMethodId, setError]);

  return (
    <div className="w-full px-1 py-1 lg:px-4">
      <div className="w-full">
        <div className="relative flex w-full flex-col items-start justify-start rounded-[30px] bg-white px-2 pt-2 pb-6 lg:flex-row lg:items-start lg:p-10">
          <div className="flex w-full flex-col items-start justify-start gap-3 px-4 lg:w-1/2">
            <PaymentMethodSelector
              selectedPaymentMethodId={selectedPaymentMethodId}
              onPaymentMethodChange={handlePaymentMethodChange}
              error={paymentMethodError}
              disabled={false}
              useWallet={useWallet}
              onWalletToggle={handleWalletToggle}
            />
          </div>
          <div className="absolute right-1/2 left-1/2 hidden h-[84%] w-[2px] -translate-x-1/2 rounded-full bg-[#EBEBEB] lg:block"></div>
          <div className="flex w-full flex-col items-start justify-between px-4 lg:w-1/2">
            <ProgramSummarySection />
            {/* Checkout Button */}

            {checkoutError && (
              <div className="flex w-full justify-center">
                <div className="p-3">
                  <p className="font-helvetica text-sm text-red-600">{checkoutError}</p>
                </div>
              </div>
            )}

            {selectedPaymentMethodId === "APPLE_PAY" ? (
              <StripeProvider>
                <div className="mt-6 w-full">
                  <ProgramApplePayButton
                    onBeforePayment={handleApplePayBeforePayment}
                    amount={Math.round(totalAmount * 100)} // Convert to cents
                    onPaymentSuccess={handleApplePaySuccess}
                  />
                </div>
              </StripeProvider>
            ) : (
              <button
                type="button"
                onClick={handleCheckout}
                disabled={!selectedPaymentMethodId}
                className="bg-primary hover:bg-primary/90 mt-4 w-full rounded-full px-4 py-3 font-medium text-white transition-colors disabled:cursor-not-allowed disabled:opacity-50"
              >
                {isBooking ? <Loader /> : "Complete Payment"}
              </button>
            )}
          </div>

          {/* <pre>{JSON.stringify(programBookingDetails, null, 2)}</pre> */}
        </div>
      </div>
    </div>
  );
};

export default ProgramCheckout;
