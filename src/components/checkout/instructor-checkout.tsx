"use client";
import { extractErrorMessage } from "@/libs/utils";
import { useBookingStore } from "@/store/booking-store";
import { useCheckoutStore } from "@/store/checkout-store";
import { ChevronLeft, ClockFadingIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

import { confirmLessonCheckout, createLessonBooking } from "@/api/booking-service";
import Loader from "../loader";
import PrimaryLinkButton from "../primary-link-button";
import { StripeProvider } from "../providers/stripe-provider";
import InstructorApplePayButton from "./instructor-apple-pay-button";
import InstructorSummarySection from "./instructor-summary-section";
import PaymentMethodSelector from "./payment-method-selector";

const InstructorCheckout = () => {
  const [selectedPaymentMethodId, setSelectedPaymentMethodId] = useState<string>("");
  const [paymentMethodError, setPaymentMethodError] = useState<string>("");
  const [useWallet, setUseWallet] = useState<boolean>(false);
  const [isBooking, setIsBooking] = useState(false);

  const router = useRouter();

  // Get booking details from booking store
  const { instructorBookingDetails, clearInstructorBooking } = useBookingStore();

  // Checkout store
  const {
    checkoutData,
    isProcessing,
    error: checkoutError,
    totalAmount,
    setCheckoutData,
    updatePaymentMethod,
    updateWalletUsage,
    setProcessing,
    setError,
    setCompleted,
    getCheckoutPayload,
    clearCheckout,
  } = useCheckoutStore();

  // Initialize checkout data when component mounts
  useEffect(() => {
    if (instructorBookingDetails && !checkoutData) {
      const initialCheckoutData = {
        booking_id: 0, // Will be set after booking creation
        payment_method: "CARD" as const,
        payment_method_id: 0,
        use_wallet: 0 as const,
      };
      setCheckoutData(initialCheckoutData);
    }
  }, [instructorBookingDetails, checkoutData, setCheckoutData]);

  // Handle payment method change
  const handlePaymentMethodChange = useCallback(
    (paymentMethodId: string) => {
      console.log("Payment method changed to:", paymentMethodId);

      if (paymentMethodId === "APPLE_PAY") {
        updatePaymentMethod("APPLE_PAY", 0);
        setSelectedPaymentMethodId(paymentMethodId);
      } else {
        setSelectedPaymentMethodId(paymentMethodId);
        updatePaymentMethod("CARD", Number(paymentMethodId));
      }

      // Clear payment method error
      if (paymentMethodError) {
        setPaymentMethodError("");
      }
    },
    [paymentMethodError, updatePaymentMethod]
  );

  // Handle wallet toggle
  const handleWalletToggle = useCallback(
    (checked: boolean) => {
      setUseWallet(checked);
      updateWalletUsage(checked);
    },
    [updateWalletUsage]
  );

  // Handler for checkout process
  const handleCheckout = useCallback(async () => {
    try {
      setIsBooking(true);
      if (!checkoutData) {
        setError("Checkout data not initialized");
        return;
      }

      if (!selectedPaymentMethodId) {
        setPaymentMethodError("Please select a payment method");
        return;
      }

      setProcessing(true);
      setError(null);
      setPaymentMethodError("");

      const payload = getCheckoutPayload();
      if (!payload) {
        throw new Error("Invalid checkout data");
      }

      console.log("Processing instructor checkout with payload:", payload);

      if (!instructorBookingDetails) {
        throw new Error("Instructor booking details not available");
      }

      // Create booking payload for instructor booking (using lesson booking API)
      const bookingPayload = {
        lesson_id: instructorBookingDetails.lesson_type_id, // Use lesson_type_id as lesson_id
        instructor_id: instructorBookingDetails.instructor_id,
        lesson_type_id: instructorBookingDetails.lesson_type_id,
        type: instructorBookingDetails.type,
        start_time: instructorBookingDetails.start_time,
        duration: instructorBookingDetails.duration,
        sub_court_id: instructorBookingDetails.sub_court_id,
        split: instructorBookingDetails.split,
        participants: instructorBookingDetails.participants || [],
      };

      console.log("Creating instructor booking with payload:", bookingPayload);

      // Use the same lesson booking API for instructor booking
      const bookingResponse = await createLessonBooking(bookingPayload);
      console.log("bookingResponse", bookingResponse);
      if (bookingResponse) {
        console.log("bookingResponse", bookingResponse.data);
        const bookingId = bookingResponse.data?.data?.id;
        const checkoutPayload = {
          payment_method: checkoutData.payment_method,
          payment_method_id: checkoutData.payment_method_id,
          id: bookingId,
        };
        const checkoutResponse = await confirmLessonCheckout(checkoutPayload);
        if (checkoutResponse) {
          console.log("checkoutResponse", checkoutResponse);
          setCompleted(true);
          clearCheckout();
          clearInstructorBooking();
          router.push(`/booking-success`);
          toast.success("Instructor Booking Payment processed successfully!");
        } else {
          throw new Error("Checkout failed");
        }
      }
    } catch (error: any) {
      console.error("Instructor checkout error:", error);
      const errorMessage = extractErrorMessage(error) || "Failed to process payment";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsBooking(false);
      setProcessing(false);
    }
  }, [
    checkoutData,
    selectedPaymentMethodId,
    instructorBookingDetails,
    setProcessing,
    setError,
    setCompleted,
    getCheckoutPayload,
    clearCheckout,
    totalAmount,
    clearInstructorBooking,
    router,
  ]);

  const handleApplePayBeforePayment = useCallback(async () => {
    try {
      if (!checkoutData) {
        setError("Checkout data not initialized");
        return null;
      }

      if (!selectedPaymentMethodId) {
        setPaymentMethodError("Please select a payment method");
        return null;
      }

      if (selectedPaymentMethodId !== "APPLE_PAY") {
        return null;
      }

      if (!instructorBookingDetails) {
        throw new Error("Instructor booking details not available");
      }

      const payload = getCheckoutPayload();
      if (!payload) {
        throw new Error("Invalid checkout data");
      }

      // Create booking payload for instructor booking (using lesson booking API)
      const bookingPayload = {
        lesson_id: instructorBookingDetails.lesson_type_id, // Use lesson_type_id as lesson_id
        instructor_id: instructorBookingDetails.instructor_id,
        lesson_type_id: instructorBookingDetails.lesson_type_id,
        type: instructorBookingDetails.type,
        start_time: instructorBookingDetails.start_time,
        duration: instructorBookingDetails.duration,
        sub_court_id: instructorBookingDetails.sub_court_id,
        split: instructorBookingDetails.split,
        participants: instructorBookingDetails.participants || [],
      };

      // Use the same lesson booking API for instructor booking
      const bookingResponse = await createLessonBooking(bookingPayload);
      console.log("bookingResponse", bookingResponse);
      if (bookingResponse) {
        console.log("bookingResponse", bookingResponse.data);
        const bookingId = bookingResponse.data?.data?.id;
        const checkoutPayload = {
          payment_method: checkoutData.payment_method,
          id: bookingId,
          use_wallet: checkoutData.use_wallet,
        };
        const checkoutResponse = await confirmLessonCheckout(checkoutPayload);
        if (checkoutResponse) {
          console.log("checkoutResponse", checkoutResponse);

          return {
            bookingId: bookingId,
            clientSecret: checkoutResponse.data?.data?.appleIntent?.client_secret,
          };
        } else {
          throw new Error("Checkout failed");
        }
      }
      return null;
    } catch (error) {
      console.error("Apple Pay before payment error:", error);
      setError(extractErrorMessage(error) || "Failed to process payment");
      return null;
    }
  }, [checkoutData, selectedPaymentMethodId, setError]);

  const handleApplePaySuccess = useCallback(async () => {
    setCompleted(true);
    clearCheckout();
    clearInstructorBooking();
    router.push(`/booking-success`);
    toast.success("Instructor Booking Payment processed successfully!");
    console.log("Apple Pay success");
  }, []);

  if (!instructorBookingDetails) {
    return (
      <div className="flex w-full flex-col items-center justify-center gap-4 py-12 text-center">
        <p className="font-helvetica text-lg font-bold text-gray-900">
          No instructor booking details available
        </p>
        <PrimaryLinkButton href="/booking" className="w-fit" text="Back to booking" />
      </div>
    );
  }

  return (
    <div className="w-full px-4 py-1">
      <div className="w-full">
        <div className="relative mb-6 flex w-full flex-col items-start justify-start rounded-[30px] bg-white p-4 lg:flex-row lg:items-start lg:p-10">
          <button
            onClick={() => router.push("/locations/charlotte?booking_type=instructor")}
            className="hover:text-primary absolute top-4 left-1 z-10 flex h-12 w-12 items-center justify-center text-gray-600 transition-colors duration-200 lg:left-4"
          >
            <ChevronLeft className="h-8 w-8" />
          </button>
          <div className="flex w-full flex-col items-start justify-start gap-3 px-8 lg:w-1/2">
            <div className="font-helvetica flex w-full items-center justify-between gap-1 pb-4 text-xl font-bold text-black">
              <div>
                <p>Instructor Booking</p>
                <p>{instructorBookingDetails.instructor_name || "Unknown Instructor"}</p>
                <p>
                  Lesson Type: {instructorBookingDetails.lesson_type_name || "Unknown Lesson Type"}
                </p>
                <p>{instructorBookingDetails.date}</p>
                <p>{instructorBookingDetails.time}</p>
              </div>
              <div className="flex flex-col items-center justify-center gap-2">
                <ClockFadingIcon className="text-primary" />
                <div className="justify-center">
                  <span className="font-helvetica text-xl leading-[25px] font-bold text-black">
                    {instructorBookingDetails.duration} min
                  </span>
                </div>
              </div>
            </div>

            {/* Payment Method Selection */}
            <div className="w-full">
              <PaymentMethodSelector
                selectedPaymentMethodId={selectedPaymentMethodId}
                onPaymentMethodChange={handlePaymentMethodChange}
                error={paymentMethodError}
                disabled={isProcessing || isBooking}
                useWallet={useWallet}
                onWalletToggle={handleWalletToggle}
              />
            </div>
          </div>

          {/* Summary Section */}
          <div className="w-full lg:w-1/2 lg:pl-8">
            <InstructorSummarySection />
            {/* Error Display */}
            {checkoutError && (
              <div className="w-full">
                <div className="p-3">
                  <p className="font-helvetica text-sm text-red-600">{checkoutError}</p>
                </div>
              </div>
            )}
            {/* Checkout Button */}
            <div className="w-full pt-4">
              {selectedPaymentMethodId === "APPLE_PAY" ? (
                <StripeProvider>
                  <div className="mt-6 w-full">
                    <InstructorApplePayButton
                      onBeforePayment={handleApplePayBeforePayment}
                      amount={Math.round(totalAmount * 100)} // Convert to cents
                      onPaymentSuccess={handleApplePaySuccess}
                    />
                  </div>
                </StripeProvider>
              ) : (
                <button
                  type="button"
                  onClick={handleCheckout}
                  disabled={!selectedPaymentMethodId || !checkoutData || isBooking || isProcessing}
                  className="bg-primary hover:bg-primary/90 w-full rounded-full px-4 py-3 font-medium text-white transition-colors disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {isBooking || isProcessing ? <Loader /> : "Complete Payment"}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InstructorCheckout;
