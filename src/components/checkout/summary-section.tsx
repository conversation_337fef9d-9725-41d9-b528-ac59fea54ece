import { Skeleton } from "@/components/ui/skeleton";
import { useBookingStore } from "@/store/booking-store";
import { useCheckoutStore } from "@/store/checkout-store";
import { AlertCircle, RefreshCw } from "lucide-react";
import React, { useMemo } from "react";

// TypeScript interface for booking estimate response
export interface BookingEstimateData {
  my_booking_base_price: number;
  my_guest_charge: number;
  booking_price: number | string;
  my_booking_price: string;
  attribute_amount: number;
  package_id: number;
  hold_amount: number;
  redeem_amount: number | string;
  discount: number;
  tax_rate: string;
  tax_amount: number;
  amount_due: number;
  total: number;
  last_time_to_pay: string;
  split: boolean;
  is_peak_hour: boolean;
  is_free_booking: boolean;
  timezone_offset: string;
}

export interface BookingEstimateResponse {
  status: string;
  message: string;
  data: { data: BookingEstimateData };
}

interface SummaryLineItemProps {
  label: string;
  amount: number;
  isDiscount?: boolean;
  isTotal?: boolean;
  className?: string;
}

const SummaryLineItem: React.FC<SummaryLineItemProps> = React.memo(
  ({ label, amount, isDiscount = false, isTotal = false, className = "" }) => (
    <div className={`flex items-center justify-between ${className}`}>
      <span
        className={`font-helvetica text-sm leading-relaxed sm:text-base ${
          isTotal ? "font-semibold text-black" : "font-normal text-gray-700"
        }`}
      >
        {label}
      </span>
      <span
        className={`font-helvetica text-right text-sm leading-relaxed sm:text-base ${
          isTotal
            ? "font-semibold text-black"
            : isDiscount
              ? "font-normal text-green-600"
              : "font-normal text-gray-900"
        }`}
      >
        {isDiscount && amount > 0 ? "-" : ""}${Math.abs(amount).toFixed(2)}
      </span>
    </div>
  )
);

SummaryLineItem.displayName = "SummaryLineItem";

interface SummarySectionProps {
  estimateData: BookingEstimateData | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

const SummarySection: React.FC<SummarySectionProps> = ({
  estimateData,
  isLoading,
  error,
  refetch,
}: SummarySectionProps) => {
  const { courtBookingDetails } = useBookingStore();
  const { setTotalAmount, setLastTimetoPay } = useCheckoutStore();

  // Calculate total attributes amount
  const totalAttributesAmount = useMemo(() => {
    return estimateData?.attribute_amount || 0;
  }, [estimateData]);

  // Format last payment time
  const formattedPaymentDeadline = useMemo(() => {
    if (!estimateData?.last_time_to_pay) return null;

    try {
      const date = new Date(estimateData.last_time_to_pay);
      const formattedDate = date.toLocaleString("en-US", {
        month: "short",
        day: "numeric",
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      });
      setLastTimetoPay(formattedDate);
      return formattedDate;
    } catch {
      return null;
    }
  }, [estimateData?.last_time_to_pay]);

  useMemo(() => {
    if (estimateData) {
      setTotalAmount(estimateData.amount_due);
    }
  }, [estimateData, setTotalAmount]);

  if (!courtBookingDetails) {
    return (
      <div className="w-full bg-white p-4">
        <div className="flex items-center justify-center py-8">
          <p className="font-helvetica text-sm text-gray-500">No booking details available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-white transition-all duration-200">
      {/* Header */}
      <div className="border-b border-gray-300 px-4 py-3 sm:px-6">
        <div className="flex items-center justify-between">
          <h3 className="font-helvetica text-lg font-semibold text-gray-900 sm:text-xl">
            Booking Summary
          </h3>
          {estimateData?.is_peak_hour && (
            <div className="flex items-center gap-1">
              <div className="h-2 w-2 rounded-full bg-yellow-400"></div>
              <span className="font-helvetica text-xs text-yellow-600 sm:text-sm">Peak Hours</span>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-4 sm:px-6">
        {isLoading ? (
          <div className="space-y-3">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="flex items-center justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-16" />
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="w-full">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <p className="font-helvetica flex-1 text-sm text-red-700">{error}</p>
              <button
                onClick={refetch}
                className="flex items-center gap-1 rounded px-2 py-1 text-xs text-red-600 transition-colors hover:bg-red-100"
                disabled={isLoading}
              >
                <RefreshCw className={`h-3 w-3 ${isLoading ? "animate-spin" : ""}`} />
                Retry
              </button>
            </div>
          </div>
        ) : estimateData ? (
          <div className="space-y-2">
            {/* Base court booking */}
            <SummaryLineItem label="Court Booking" amount={estimateData.my_booking_base_price} />

            {/* Guest charges */}
            {estimateData.my_guest_charge > 0 && (
              <SummaryLineItem label="Guest Charges" amount={estimateData.my_guest_charge} />
            )}

            {/* Extras/Attributes */}
            {totalAttributesAmount > 0 && (
              <SummaryLineItem label="Extras & Add-ons" amount={totalAttributesAmount} />
            )}

            {/* Package discount */}
            {estimateData.package_id > 0 && estimateData.discount > 0 && (
              <SummaryLineItem
                label="Package Discount"
                amount={estimateData.discount}
                isDiscount={true}
              />
            )}

            {/* Tax */}
            {estimateData.tax_amount > 0 && (
              <SummaryLineItem
                label={`Tax (${estimateData.tax_rate}%)`}
                amount={estimateData.tax_amount}
              />
            )}

            {/* Total */}
            <SummaryLineItem
              label="Total Amount"
              amount={estimateData.amount_due}
              isTotal={true}
              className="border-t border-dashed border-gray-200 pt-2"
            />

            <SummaryLineItem
              label="Hold Amount"
              amount={estimateData.hold_amount}
              className="border-t border-dashed border-gray-200 pt-2"
            />

            {/* Hold amount info */}
            {/* {estimateData.hold_amount > 0 && (
              <div className="mt-4 rounded-md bg-blue-50 p-3">
                <div className="flex items-start gap-2">
                  <Clock className="text-primary mt-0.5 h-4 w-4" />
                  <div className="flex-1">
                    {formattedPaymentDeadline && (
                      <p className="font-helvetica text-primary mt-1 text-xs">
                        Complete payment by {formattedPaymentDeadline}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )} */}

            {/* Free booking indicator */}
            {estimateData?.is_free_booking && (
              <div className="mt-3 rounded-md py-2">
                <p className="font-helvetica text-sm text-green-800">
                  🎉 This is a complimentary booking!
                </p>
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center justify-center py-4">
            <p className="font-helvetica text-sm text-gray-500">Loading estimate...</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SummarySection;
