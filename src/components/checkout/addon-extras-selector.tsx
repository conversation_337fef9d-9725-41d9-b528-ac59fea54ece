"use client";
import { useGetCourtAttributeList } from "@/api/booking-checkout-service";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { useBookingStore } from "@/store/booking-store";
import { CourtAttribute, useCheckoutStore } from "@/store/checkout-store";
import { ChevronDown, Minus, Plus } from "lucide-react";
import Image from "next/image";
import { useCallback, useEffect, useMemo, useState } from "react";

interface AddonExtrasSelectorProps {}

const AddonExtrasSelector = ({}: AddonExtrasSelectorProps) => {
  const [isUpdating, setIsUpdating] = useState(false);
  // Note: bookingId is currently not used but may be needed for future API filtering
  const { selectedCourtAttributes, updateCourtAttribute } = useCheckoutStore();
  const { courtBookingDetails, replaceAttributes } = useBookingStore();
  const { courtAttributeList, courtAttributeListLoading, courtAttributeListError } =
    useGetCourtAttributeList({ court_id: courtBookingDetails?.court_id || 0 });

  console.log("courtAttributeList", courtAttributeList);

  // Sync checkout store with booking store attributes on mount
  useEffect(() => {
    if (courtBookingDetails?.attributes && courtBookingDetails.attributes.length > 0) {
      // Initialize checkout store with existing booking store attributes
      courtBookingDetails.attributes.forEach((attr) => {
        updateCourtAttribute(attr.court_attribute_id, attr.value);
      });
    }
  }, [courtBookingDetails?.attributes, updateCourtAttribute]);

  // Type the court attributes list
  const typedCourtAttributes = useMemo((): CourtAttribute[] => {
    // Handle case where courtAttributeList might be an object or array
    if (!courtAttributeList) return [];

    // If it's already an array, return it
    if (Array.isArray(courtAttributeList)) {
      return courtAttributeList as CourtAttribute[];
    }

    // If it's an object, convert it to an array of values
    if (typeof courtAttributeList === "object") {
      return Object.values(courtAttributeList) as CourtAttribute[];
    }

    return [];
  }, [courtAttributeList]);

  // Get the icon path based on the attribute name
  const getAttributeIcon = useCallback((attribute: CourtAttribute) => {
    // if (attribute.icon_picture) {
    //   return attribute.icon_picture;
    // }

    // Fallback to default icons based on name
    const name = attribute?.court_attribute?.name?.toLowerCase() || "";
    if (name.includes("ball")) {
      return "/imgs/checkout/ball.png";
    } else {
      return "/imgs/checkout/racket.png";
    }
  }, []);

  // Get current quantity for an attribute
  const getAttributeQuantity = useCallback(
    (attributeId: number) => {
      const selected = selectedCourtAttributes.find(
        (attr) => attr.court_attribute_id === attributeId
      );
      return selected ? selected.value : 0;
    },
    [selectedCourtAttributes]
  );

  // Handle quantity change
  const handleQuantityChange = useCallback(
    (attributeId: number, value: number) => {
      updateCourtAttribute(attributeId, value);
    },
    [updateCourtAttribute]
  );

  // Calculate total selected items
  const totalSelectedItems = useMemo(() => {
    return selectedCourtAttributes.reduce((total, attr) => total + attr.value, 0);
  }, [selectedCourtAttributes]);

  // Calculate total amount
  const totalAmount = useMemo(() => {
    return selectedCourtAttributes.reduce((total, selectedAttr) => {
      const attribute = typedCourtAttributes.find(
        (attr) => attr.court_attribute_id === selectedAttr.court_attribute_id
      );
      if (attribute) {
        const price = parseFloat(attribute.after_discount || attribute.amount);
        return total + price * selectedAttr.value;
      }
      return total;
    }, 0);
  }, [selectedCourtAttributes, typedCourtAttributes]);

  // Handle increment/decrement with proper validation
  const handleIncrement = useCallback(
    (attribute: CourtAttribute) => {
      const currentQuantity = getAttributeQuantity(attribute.court_attribute_id);
      if (
        !attribute.max_value ||
        (currentQuantity < attribute.max_value && currentQuantity >= (attribute.min_value || 0))
      ) {
        const newValue = attribute.max_value
          ? Math.min(currentQuantity + 1, attribute.max_value)
          : currentQuantity + 1;
        handleQuantityChange(attribute.court_attribute_id, newValue);
        return;
      }
      if (currentQuantity < (attribute.min_value || 0)) {
        handleQuantityChange(attribute.court_attribute_id, attribute.min_value || 0);
        return;
      }
    },
    [getAttributeQuantity, handleQuantityChange]
  );

  const handleDecrement = useCallback(
    (attribute: CourtAttribute) => {
      const currentQuantity = getAttributeQuantity(attribute.court_attribute_id);
      console.log("Decrementing...", currentQuantity);
      if (currentQuantity > (attribute.min_value || 0)) {
        const newValue = Math.max(currentQuantity - 1, attribute.min_value || 0);
        handleQuantityChange(attribute.court_attribute_id, newValue);
        return;
      }

      if (currentQuantity === attribute.min_value) {
        handleQuantityChange(attribute.court_attribute_id, 0);
        return;
      }
    },
    [getAttributeQuantity, handleQuantityChange]
  );

  if (courtAttributeListLoading) {
    return (
      <div className="space-y-4">
        <div className="font-helvetica text-lg font-semibold text-gray-900">
          Add-on Extras <span className="font-normal text-gray-500">(rackets, balls, etc)</span>
        </div>
        <Skeleton className="h-10 w-32 rounded-full" />
      </div>
    );
  }

  if (courtAttributeListError) {
    return (
      <div className="space-y-4">
        <div className="font-helvetica text-lg font-semibold text-gray-900">
          Add-on Extras <span className="font-normal text-gray-500">(rackets, balls, etc)</span>
        </div>
        <div className="text-sm text-red-600">Failed to load court extras. Please try again.</div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-4 pb-4">
      <div className="font-helvetica text-lg font-semibold text-gray-900">
        Add-on Extras <span className="font-normal text-gray-500">(rackets, balls, etc)</span>
      </div>

      <DropdownMenu>
        <DropdownMenuTrigger
          disabled={courtAttributeListLoading}
          className={`flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-6 py-4 transition-colors ${
            courtAttributeListLoading ? "cursor-not-allowed opacity-50" : "hover:bg-gray-50"
          }`}
        >
          <span className="font-helvetica text-[15px] font-normal text-black">Add Extras</span>
          <div className="flex items-center gap-1">
            {totalSelectedItems > 0 && (
              <span className="bg-primary ml-1 rounded-full px-2 py-0.5 text-xs text-white">
                {totalSelectedItems}
              </span>
            )}
            <ChevronDown className="h-4 w-4" />
          </div>
        </DropdownMenuTrigger>

        <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] min-w-[var(--radix-dropdown-menu-trigger-width)] rounded-[20px] bg-white p-4 shadow-lg">
          <div className="w-full space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-helvetica text-lg font-semibold text-gray-900">Court Extras</h3>
              {totalAmount > 0 && (
                <div className="text-primary text-sm font-medium">
                  Total: ${totalAmount.toFixed(2)}
                </div>
              )}
            </div>

            <div className="space-y-3">
              {typedCourtAttributes.map((attribute) => {
                const quantity = getAttributeQuantity(attribute.court_attribute_id);
                const price = parseFloat(attribute.after_discount || attribute.amount);
                const isDecrementDisabled = courtAttributeListLoading || quantity <= 0;
                const isIncrementDisabled =
                  courtAttributeListLoading ||
                  Boolean(attribute.max_value && quantity >= attribute.max_value);

                return (
                  <div
                    key={attribute.court_attribute_id}
                    className="flex items-center justify-between rounded-lg bg-white px-3 py-1"
                  >
                    {/* Icon and Details */}
                    <div className="flex items-center gap-3">
                      <div className="flex-shrink-0">
                        <Image
                          src={getAttributeIcon(attribute)}
                          alt={attribute?.court_attribute?.iconPicture?.alt || "icon"}
                          width={25}
                          height={25}
                          className="object-contain"
                        />
                      </div>
                      <div className="flex flex-col">
                        <span className="font-helvetica text-[15px] font-medium text-black capitalize">
                          {attribute?.court_attribute?.name || "No name"}
                        </span>
                        <div className="flex items-center gap-1">
                          <span className="font-helvetica text-[15px] font-medium text-[#1c5534]">
                            +${price.toFixed(0)}
                          </span>
                          <span className="font-helvetica text-[15px] font-normal text-black">
                            per unit {attribute?.min_value > 0 && `(min: ${attribute?.min_value})`}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Quantity Controls */}
                    <div className="flex items-center gap-3">
                      {/* Decrement Button */}
                      <button
                        type="button"
                        onClick={() => handleDecrement(attribute)}
                        disabled={isDecrementDisabled}
                        className={`flex h-10 w-10 items-center justify-center rounded-full border border-gray-300 bg-white transition-colors ${
                          isDecrementDisabled
                            ? "cursor-not-allowed opacity-50"
                            : "hover:border-gray-400 hover:bg-gray-50"
                        }`}
                      >
                        <Minus className="h-4 w-4 text-gray-600" />
                      </button>

                      {/* Quantity Display */}
                      <div className="flex min-w-[3rem] items-center justify-center">
                        <span className="font-helvetica text-[15px] font-medium text-black">
                          {quantity}
                        </span>
                      </div>

                      {/* Increment Button */}
                      <button
                        type="button"
                        onClick={() => handleIncrement(attribute)}
                        disabled={isIncrementDisabled}
                        className={`flex h-10 w-10 items-center justify-center rounded-full border border-gray-300 bg-white transition-colors ${
                          isIncrementDisabled
                            ? "cursor-not-allowed opacity-50"
                            : "hover:border-gray-400 hover:bg-gray-50"
                        }`}
                      >
                        <Plus className="h-4 w-4 text-gray-600" />
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* {totalSelectedItems > 0 && (
              <div className="border-t pt-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium text-gray-900">
                    Total Items: {totalSelectedItems}
                  </span>
                  <span className="text-primary font-bold">${totalAmount.toFixed(2)}</span>
                </div>
              </div>
            )} */}
            {/* <button
              type="button"
              onClick={handleUpdateAddons}
              className="bg-primary hover:bg-primary/90 mt-4 w-full rounded-full px-4 py-3 font-medium text-white transition-colors disabled:cursor-not-allowed disabled:opacity-50"
            >
              {isUpdating ? <Loader /> : "Update Addons"}
            </button> */}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      {typedCourtAttributes.length === 0 && (
        <div className="text-sm text-gray-500">No court extras available for this booking.</div>
      )}
    </div>
  );
};

export default AddonExtrasSelector;
