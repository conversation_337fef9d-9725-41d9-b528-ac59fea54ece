import { useLessonBookingEstimate } from "@/hooks/use-booking-estimate";
import { useBookingStore } from "@/store/booking-store";
import { useCheckoutStore } from "@/store/checkout-store";
import { AlertCircle, RefreshCw } from "lucide-react";
import React, { useMemo } from "react";
import { Skeleton } from "../ui/skeleton";
import SummaryLineItem from "./summary-line-item";

const InstructorSummarySection: React.FC = () => {
  const { instructorBookingDetails } = useBookingStore();
  const { setTotalAmount, setLastTimetoPay } = useCheckoutStore();

  // Prepare estimate request data
  const estimateRequestData = useMemo(() => {
    if (!instructorBookingDetails) return null;

    return {
      lesson_id: instructorBookingDetails.lesson_type_id,
      instructor_id: instructorBookingDetails.instructor_id,
      lesson_type_id: instructorBookingDetails.lesson_type_id,
      type: instructorBookingDetails.type,
      start_time: instructorBookingDetails.start_time,
      duration: instructorBookingDetails.duration,
      sub_court_id: instructorBookingDetails.sub_court_id,
      split: instructorBookingDetails.split,
      participants: instructorBookingDetails.participants || [],
    };
  }, [instructorBookingDetails]);

  const { estimateData, isLoading, error, refetch } = useLessonBookingEstimate(estimateRequestData);

  console.log("estimateData", estimateData);

  useMemo(() => {
    if (estimateData) {
      setTotalAmount(estimateData.total);
    }
  }, [estimateData, setTotalAmount]);

  if (!instructorBookingDetails) {
    return (
      <div className="w-full rounded-lg border border-gray-200 bg-white p-6">
        <p className="text-center text-gray-500">No booking details available</p>
      </div>
    );
  }

  return (
    <div className="w-full rounded-lg bg-white transition-all duration-200">
      {/* Header */}
      <div className="border-b border-gray-100 px-4 py-3 sm:px-6">
        <div className="flex items-center justify-between">
          <h3 className="font-helvetica text-lg font-semibold text-gray-900 sm:text-xl">
            Booking Summary
          </h3>
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-4 sm:px-6">
        {isLoading ? (
          <div className="space-y-3">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-center justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-16" />
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="w-full">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <p className="font-helvetica flex-1 text-sm text-red-700">{error}</p>
              <button
                onClick={refetch}
                className="flex items-center gap-1 rounded px-2 py-1 text-xs text-red-600 transition-colors hover:bg-red-100"
                disabled={isLoading}
              >
                <RefreshCw className={`h-3 w-3 ${isLoading ? "animate-spin" : ""}`} />
                Retry
              </button>
            </div>
          </div>
        ) : estimateData ? (
          <div className="space-y-2">
            {/* Base lesson booking */}
            <SummaryLineItem label="Lesson Booking" amount={estimateData.booking_price || 0} />

            {/* Package discount */}
            {estimateData.package_id > 0 && (
              <SummaryLineItem label="Package Applied" amount={0} isDiscount={false} />
            )}

            {/* Tax */}
            {estimateData.tax_amount > 0 && (
              <SummaryLineItem
                label={`Tax (${estimateData.tax_rate}%)`}
                amount={estimateData.tax_amount}
              />
            )}

            {/* Total */}
            <SummaryLineItem
              label="Total Amount"
              amount={estimateData.total}
              isTotal={true}
              className="border-t border-dashed border-gray-200 pt-2"
            />

            {/* Due Amount */}
            {estimateData.due_amount > 0 && (
              <SummaryLineItem
                label="Due Amount"
                amount={estimateData.due_amount}
                className="border-t border-dashed border-gray-200 pt-2"
              />
            )}

            {/* Show lesson type info */}
            {estimateData.lesson_type && (
              <div className="mt-3 rounded-md">
                <p className="font-helvetica text-primary text-sm font-medium">
                  {estimateData.lesson_type.name} - {estimateData.price?.type || "Hourly"} Rate
                </p>
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center justify-center py-4">
            <p className="font-helvetica text-sm text-gray-500">Loading estimate...</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default InstructorSummarySection;
