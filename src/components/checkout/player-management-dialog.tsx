import { addPlayer, removePlayer } from "@/api/booking-checkout-service";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import * as Dialog from "@radix-ui/react-dialog";
import { ArrowLeft, Plus, X } from "lucide-react";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import Loader from "../loader";

// Player interface for the split payment
interface Player {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  country_code?: string;
  isSelected: boolean;
  isCurrentUser?: boolean;
}

// Interface for adding new player
interface NewPlayerData {
  name: string;
  email: string;
  country_code: string;
  phone: string;
}

interface PlayerManagementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  players: Player[];
  onPlayerToggle: (playerId: string) => void;
  selectedPlayersCount: number;
  maxPlayers: number;
  playerListLoading: boolean;
  revalidateGetPlayerList: () => void;
  isLessonBooking?: boolean; // New prop to distinguish lesson vs court booking
}

type DialogMode = "select" | "add";

const PlayerManagementDialog: React.FC<PlayerManagementDialogProps> = ({
  isOpen,
  onClose,
  players,
  onPlayerToggle,
  selectedPlayersCount,
  maxPlayers,
  playerListLoading,
  revalidateGetPlayerList,
  isLessonBooking = false,
}) => {
  const [dialogMode, setDialogMode] = useState<DialogMode>("select");
  const [isAddingPlayer, setIsAddingPlayer] = useState(false);
  const [isRemovingPlayer, setIsRemovingPlayer] = useState<string | null>(null);
  const [playerToRemove, setPlayerToRemove] = useState<{ id: string; name: string } | null>(null);

  // New player form data with memoized initial state
  const initialPlayerData = useMemo(
    () => ({
      name: "",
      email: "",
      country_code: "+1",
      phone: "",
    }),
    []
  );

  const [newPlayerData, setNewPlayerData] = useState<NewPlayerData>(initialPlayerData);

  // Memoized validation for form submission
  const isFormValid = useMemo(() => {
    return newPlayerData.name.trim() && newPlayerData.email.trim() && newPlayerData.phone.trim();
  }, [newPlayerData]);

  // Reset dialog state when it opens/closes
  useEffect(() => {
    if (isOpen) {
      setDialogMode("select");
      setNewPlayerData(initialPlayerData);
      setIsAddingPlayer(false);
      setIsRemovingPlayer(null);
      setPlayerToRemove(null);
    }
  }, [isOpen, initialPlayerData]);

  // Handle switching to add player mode
  const handleSwitchToAddMode = useCallback(() => {
    setDialogMode("add");
  }, []);

  // Handle switching back to select mode
  const handleSwitchToSelectMode = useCallback(() => {
    setDialogMode("select");
    setNewPlayerData(initialPlayerData);
  }, [initialPlayerData]);

  // Handle adding new player with improved error handling and state management
  const handleAddPlayer = useCallback(async () => {
    if (!isFormValid) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsAddingPlayer(true);
    try {
      await addPlayer(newPlayerData);
      toast.success("Player added successfully");

      // Revalidate the player list to get updated data
      revalidateGetPlayerList();

      // Reset form and switch back to select mode
      setNewPlayerData(initialPlayerData);
      setDialogMode("select");
    } catch (error: any) {
      console.error("Failed to add player:", error);
      toast.error(error?.message || "Failed to add player");
    } finally {
      setIsAddingPlayer(false);
    }
  }, [isFormValid, newPlayerData, revalidateGetPlayerList, initialPlayerData]);

  // Handle dialog close
  const handleClose = useCallback(() => {
    setDialogMode("select");
    setNewPlayerData(initialPlayerData);
    onClose();
  }, [onClose, initialPlayerData]);

  // Handle showing remove player confirmation dialog
  const handleShowRemoveConfirmation = useCallback((playerId: string, playerName: string) => {
    setPlayerToRemove({ id: playerId, name: playerName });
  }, []);

  // Handle confirmed player removal
  const handleConfirmRemovePlayer = useCallback(async () => {
    if (!playerToRemove) return;

    const { id: playerId } = playerToRemove;
    setIsRemovingPlayer(playerId);
    setPlayerToRemove(null);

    try {
      await removePlayer(parseInt(playerId));
      toast.success("Player removed successfully");

      // If the removed player was selected, deselect them first
      const removedPlayer = players.find((p) => p.id === playerId);
      if (removedPlayer?.isSelected) {
        onPlayerToggle(playerId);
      }

      // Revalidate the player list to get updated data
      revalidateGetPlayerList();
    } catch (error: any) {
      console.error("Failed to remove player:", error);
      toast.error(error?.message || "Failed to remove player");
    } finally {
      setIsRemovingPlayer(null);
    }
  }, [playerToRemove, players, revalidateGetPlayerList, onPlayerToggle]);

  // Handle canceling player removal
  const handleCancelRemovePlayer = useCallback(() => {
    setPlayerToRemove(null);
  }, []);

  // Memoized form field handlers for better performance
  const handleNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setNewPlayerData((prev) => ({ ...prev, name: e.target.value }));
  }, []);

  const handleEmailChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const email = e.target.value;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    setNewPlayerData((prev) => ({ ...prev, email: e.target.value }));
  }, []);

  const handleCountryCodeChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setNewPlayerData((prev) => ({ ...prev, country_code: e.target.value }));
  }, []);

  const handlePhoneChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/\D/g, "");
    if (value.length > 10) {
      value = value.slice(0, 10);
    }
    setNewPlayerData((prev) => ({ ...prev, phone: value }));
  }, []);

  return (
    <Dialog.Root open={isOpen} onOpenChange={handleClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[2000] bg-black/50" />
        <Dialog.Content className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-[2001] w-[96%] max-w-lg translate-x-[-50%] translate-y-[-50%] rounded-[20px] bg-white p-5 shadow-lg duration-200 lg:w-full">
          <Dialog.Close className="focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-2 right-2 rounded-full opacity-70 ring-0 transition-opacity hover:opacity-100 focus:ring-0 focus:outline-none disabled:pointer-events-none">
            <X className="h-8 w-8 text-gray-600 outline-none" />
            <span className="sr-only">Close</span>
          </Dialog.Close>

          {/* Back button for add mode */}
          {dialogMode === "add" && (
            <button
              onClick={handleSwitchToSelectMode}
              className="absolute top-2 left-2 rounded-full p-2 opacity-70 transition-opacity hover:opacity-100 focus:outline-none"
            >
              <ArrowLeft className="h-6 w-6 text-gray-600" />
              <span className="sr-only">Back</span>
            </button>
          )}

          {dialogMode === "select" ? (
            <>
              <Dialog.Title className="text-lg font-medium text-gray-900">
                Select Players
              </Dialog.Title>
              <Dialog.Description className="mb-4 text-sm text-gray-500">
                {isLessonBooking
                  ? `Select additional participants for the lesson. Maximum ${maxPlayers} additional participants allowed.`
                  : `Choose who will be playing and splitting the payment. Maximum ${maxPlayers} players allowed.`}
              </Dialog.Description>

              <div className="max-h-96 space-y-4 overflow-y-auto">
                {playerListLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
                  </div>
                ) : (
                  players.map((player) => (
                    <div
                      key={player.id}
                      className={`flex items-center justify-between rounded-full border p-3 transition-colors ${
                        player.isSelected
                          ? "border-primary bg-primary/5"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full">
                            <span className="text-primary text-sm font-medium">
                              {player.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">{player.name}</p>
                          {player?.email && <p className="text-xs text-gray-500">{player.email}</p>}
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        {/* Delete button - only show for non-current users */}
                        {!player.isCurrentUser && (
                          <button
                            onClick={() => handleShowRemoveConfirmation(player.id, player.name)}
                            disabled={isRemovingPlayer === player.id}
                            className="flex h-6 w-6 items-center justify-center rounded-full text-gray-400 transition-colors hover:bg-red-50 hover:text-red-500 disabled:opacity-50"
                            title={`Remove ${player.name}`}
                          >
                            {isRemovingPlayer === player.id ? (
                              <div className="h-3 w-3 animate-spin rounded-full border border-red-500 border-t-transparent" />
                            ) : (
                              <X className="h-3 w-3" />
                            )}
                          </button>
                        )}

                        {/* Selection checkbox */}
                        <button
                          onClick={() => onPlayerToggle(player.id)}
                          disabled={!player.isSelected && selectedPlayersCount >= maxPlayers}
                          className={`flex h-6 w-6 items-center justify-center rounded border-2 transition-colors ${
                            player.isSelected
                              ? "border-primary bg-primary text-white"
                              : "hover:border-primary border-gray-300"
                          } ${
                            isLessonBooking
                              ? // For lessons: only disable if at max and not selected
                                !player.isSelected && selectedPlayersCount >= maxPlayers
                                ? "cursor-not-allowed opacity-50"
                                : "cursor-pointer"
                              : // For court bookings: original logic
                                (player.isCurrentUser && player.isSelected) ||
                                  (!player.isSelected && selectedPlayersCount >= maxPlayers) ||
                                  (player.isSelected &&
                                    !player.isCurrentUser &&
                                    selectedPlayersCount <= 2)
                                ? "cursor-not-allowed opacity-50"
                                : "cursor-pointer"
                          }`}
                        >
                          {player.isSelected && (
                            <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>
                  ))
                )}
              </div>

              <div className="flex justify-between space-x-2 pt-4">
                <button
                  onClick={handleSwitchToAddMode}
                  className="bg-primary hover:bg-primary/90 flex items-center gap-2 rounded-full px-4 py-2 text-white transition-colors"
                >
                  <Plus className="h-4 w-4" />
                  Add New Player
                </button>
                <button
                  onClick={handleClose}
                  className="rounded-full bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-200"
                >
                  Done
                </button>
              </div>
            </>
          ) : (
            <>
              <Dialog.Title className="ml-8 text-lg font-medium text-gray-900">
                Add New Player
              </Dialog.Title>
              <Dialog.Description className="mb-4 ml-8 text-sm text-gray-500">
                Add a new player to your list for future bookings.
              </Dialog.Description>

              <div className="space-y-4">
                {/* Name Field */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Name</label>
                  <input
                    type="text"
                    className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
                    value={newPlayerData.name}
                    onChange={handleNameChange}
                    placeholder="Enter player name"
                    disabled={isAddingPlayer}
                  />
                </div>

                {/* Email Field */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Email</label>
                  <input
                    type="email"
                    className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
                    value={newPlayerData.email}
                    onChange={handleEmailChange}
                    placeholder="Enter email address"
                    disabled={isAddingPlayer}
                  />
                </div>

                {/* Phone Field */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Phone Number</label>
                  <div className="flex overflow-hidden rounded-full border border-gray-300">
                    <select
                      value={newPlayerData.country_code}
                      onChange={handleCountryCodeChange}
                      className="border-r border-gray-300 bg-gray-50 px-3 py-2 text-sm focus:outline-none"
                      disabled={isAddingPlayer}
                    >
                      <option value="+1">🇺🇸 +1</option>
                    </select>
                    <input
                      type="tel"
                      placeholder="Mobile Number"
                      value={newPlayerData.phone}
                      onChange={handlePhoneChange}
                      className="flex-1 rounded-full px-3 py-2 text-black focus:outline-none"
                      disabled={isAddingPlayer}
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <button
                  onClick={handleSwitchToSelectMode}
                  className="rounded-full bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-200"
                  disabled={isAddingPlayer}
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddPlayer}
                  disabled={isAddingPlayer || !isFormValid}
                  className="bg-primary hover:bg-primary/90 rounded-full px-4 py-2 text-sm font-medium text-white transition-colors disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {isAddingPlayer ? <Loader /> : "Add Player"}
                </button>
              </div>
            </>
          )}
        </Dialog.Content>
      </Dialog.Portal>

      {/* Remove Player Confirmation Dialog */}
      <AlertDialog open={!!playerToRemove} onOpenChange={handleCancelRemovePlayer}>
        <AlertDialogContent className="z-[2002] max-w-md border-1 border-gray-200 bg-white">
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Player</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove <strong>{playerToRemove?.name}</strong> from your
              player list? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              className="bg-white text-gray-700 hover:bg-gray-200"
              onClick={handleCancelRemovePlayer}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmRemovePlayer}
              className="bg-red-600 text-white hover:bg-red-700"
            >
              Remove Player
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Dialog.Root>
  );
};

export default PlayerManagementDialog;
