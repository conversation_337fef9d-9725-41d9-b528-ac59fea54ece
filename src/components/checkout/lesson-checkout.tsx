import { confirmLessonCheckout, createLessonBooking } from "@/api/booking-service";
import { extractErrorMessage } from "@/libs/utils";
import { useBookingStore } from "@/store/booking-store";
import { useCheckoutStore } from "@/store/checkout-store";
import { ChevronLeft, ClockFadingIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import ValidationError from "../forms/validation-error";
import Loader from "../loader";
import PrimaryLinkButton from "../primary-link-button";
import { StripeProvider } from "../providers/stripe-provider";
import InstructorApplePayButton from "./instructor-apple-pay-button";
import LessonSummarySection from "./lesson-summary-section";
import PaymentMethodSelector from "./payment-method-selector";

const LessonCheckout = () => {
  const [selectedPaymentMethodId, setSelectedPaymentMethodId] = useState<string>("");
  const [paymentMethodError, setPaymentMethodError] = useState<string>("");
  const [useWallet, setUseWallet] = useState<boolean>(false);
  const [isBooking, setIsBooking] = useState(false);

  const router = useRouter();

  // Get booking details from booking store
  const { lessonBookingDetails, clearLessonBooking } = useBookingStore();

  // Checkout store
  const {
    checkoutData,
    isProcessing,
    error: checkoutError,
    setCheckoutData,
    updatePaymentMethod,
    updateWalletUsage,
    setProcessing,
    setError,
    setCompleted,
    getCheckoutPayload,
    clearCheckout,
    totalAmount,
  } = useCheckoutStore();

  console.log("lessonBookingDetails from store", lessonBookingDetails);

  // Initialize checkout data when booking details are available
  useEffect(() => {
    if (lessonBookingDetails && !checkoutData) {
      // Since we don't have a booking_id yet (booking will be created during checkout),
      // we'll use a temporary ID of 0 and update it when the booking is created
      setCheckoutData({
        booking_id: 0, // Temporary - will be updated when booking is created
        payment_method: "CARD", // Default to CARD
        payment_method_id: 0, // Will be set by PaymentMethodSelector
        use_wallet: 0,
      });
    }
  }, [lessonBookingDetails, checkoutData, setCheckoutData]);

  // Handle payment method change
  const handlePaymentMethodChange = useCallback(
    (paymentMethodId: string) => {
      console.log("Payment method changed to:", paymentMethodId);

      if (paymentMethodId === "APPLE_PAY") {
        updatePaymentMethod("APPLE_PAY", 0);
        setSelectedPaymentMethodId(paymentMethodId);
      } else {
        setSelectedPaymentMethodId(paymentMethodId);
        updatePaymentMethod("CARD", Number(paymentMethodId));
      }

      // Clear payment method error
      if (paymentMethodError) {
        setPaymentMethodError("");
      }
    },
    [paymentMethodError, updatePaymentMethod]
  );

  // Handler for wallet toggle
  const handleWalletToggle = useCallback(
    (checked: boolean) => {
      setUseWallet(checked);
      updateWalletUsage(checked);
    },
    [updateWalletUsage]
  );

  // Handler for checkout process
  const handleCheckout = useCallback(async () => {
    try {
      setIsBooking(true);
      if (!checkoutData) {
        setError("Checkout data not initialized");
        return;
      }

      if (!selectedPaymentMethodId) {
        setPaymentMethodError("Please select a payment method");
        return;
      }

      setProcessing(true);
      setError(null);
      setPaymentMethodError("");

      const payload = getCheckoutPayload();
      if (!payload) {
        throw new Error("Invalid checkout data");
      }

      console.log("Processing lesson checkout with payload:", payload);

      if (!lessonBookingDetails) {
        throw new Error("Lesson booking details not available");
      }

      const bookingPayload: any = {
        instructor_id: lessonBookingDetails.instructor_id,
        lesson_type_id: lessonBookingDetails.lesson_type_id,
        type: lessonBookingDetails.type,
        start_time: lessonBookingDetails.start_time,
        duration: lessonBookingDetails.duration,
        sub_court_id: lessonBookingDetails.sub_court_id,
      };

      if (lessonBookingDetails.participants && lessonBookingDetails.participants.length > 0) {
        bookingPayload["participants"] = lessonBookingDetails.participants;
      }

      console.log("checkout bookingPayload", bookingPayload);

      const bookingResponse = await createLessonBooking(bookingPayload);
      console.log("bookingResponse", bookingResponse);
      if (bookingResponse) {
        console.log("bookingResponse", bookingResponse.data);
        const bookingId = bookingResponse.data?.data?.id;
        const checkoutPayload = {
          payment_method: checkoutData.payment_method,
          payment_method_id: checkoutData.payment_method_id,
          id: bookingId,
        };
        const checkoutResponse = await confirmLessonCheckout(checkoutPayload);
        if (checkoutResponse) {
          console.log("checkoutResponse", checkoutResponse);
          setCompleted(true);
          clearCheckout();
          clearLessonBooking();
          router.push(`/booking-success`);
          toast.success("Lesson Booking Payment processed successfully!");
        } else {
          throw new Error("Checkout failed");
        }
      }
    } catch (error: any) {
      console.error("Lesson checkout error:", error);
      const errorMessage = extractErrorMessage(error) || "Failed to process payment";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsBooking(false);
    }
  }, [
    checkoutData,
    selectedPaymentMethodId,
    lessonBookingDetails,
    setProcessing,
    setError,
    setCompleted,
    getCheckoutPayload,
    clearCheckout,
    clearLessonBooking,
    router,
  ]);

  const handleApplePayBeforePayment = useCallback(async () => {
    try {
      if (!checkoutData) {
        setError("Checkout data not initialized");
        return null;
      }

      if (!selectedPaymentMethodId) {
        setPaymentMethodError("Please select a payment method");
        return null;
      }

      if (selectedPaymentMethodId !== "APPLE_PAY") {
        return null;
      }

      if (!lessonBookingDetails) {
        throw new Error("Lesson booking details not available");
      }

      const payload = getCheckoutPayload();
      if (!payload) {
        throw new Error("Invalid checkout data");
      }

      const bookingPayload: any = {
        instructor_id: lessonBookingDetails.instructor_id,
        lesson_type_id: lessonBookingDetails.lesson_type_id,
        type: lessonBookingDetails.type,
        start_time: lessonBookingDetails.start_time,
        duration: lessonBookingDetails.duration,
        sub_court_id: lessonBookingDetails.sub_court_id,
      };

      if (lessonBookingDetails.participants && lessonBookingDetails.participants.length > 0) {
        bookingPayload["participants"] = lessonBookingDetails.participants;
      }

      // Use the same lesson booking API for instructor booking
      const bookingResponse = await createLessonBooking(bookingPayload);
      console.log("bookingResponse", bookingResponse);
      if (bookingResponse) {
        console.log("bookingResponse", bookingResponse.data);
        const bookingId = bookingResponse.data?.data?.id;
        const checkoutPayload = {
          payment_method: checkoutData.payment_method,
          id: bookingId,
          use_wallet: checkoutData.use_wallet,
        };
        const checkoutResponse = await confirmLessonCheckout(checkoutPayload);
        if (checkoutResponse) {
          console.log("checkoutResponse", checkoutResponse);

          return {
            bookingId: bookingId,
            clientSecret: checkoutResponse.data?.data?.appleIntent?.client_secret,
          };
        } else {
          throw new Error("Checkout failed");
        }
      }
      return null;
    } catch (error) {
      console.error("Apple Pay before payment error:", error);
      setError(extractErrorMessage(error) || "Failed to process payment");
      return null;
    }
  }, [checkoutData, selectedPaymentMethodId, setError]);

  const handleApplePaySuccess = useCallback(async () => {
    setCompleted(true);
    clearCheckout();
    clearLessonBooking();
    router.push(`/booking-success`);
    toast.success("Instructor Booking Payment processed successfully!");
    console.log("Apple Pay success");
  }, []);

  if (!lessonBookingDetails) {
    return (
      <div className="flex w-full flex-col items-center justify-center gap-4 py-12 text-center">
        <p className="font-helvetica text-lg font-bold text-gray-900">
          No lesson booking details available
        </p>
        <PrimaryLinkButton href="/locations/charlotte" className="w-fit" text="Back to booking" />
      </div>
    );
  }

  return (
    <div className="w-full px-4 py-1">
      <div className="w-full">
        <div className="relative mb-6 flex w-full flex-col items-start justify-start rounded-[30px] bg-white p-4 lg:flex-row lg:items-start lg:p-10">
          <button
            onClick={() => router.push("/locations/charlotte?booking_type=lesson")}
            className="hover:text-primary absolute top-4 left-1 z-10 flex h-12 w-12 items-center justify-center text-gray-600 transition-colors duration-200 lg:left-4"
          >
            <ChevronLeft className="h-8 w-8" />
          </button>
          <div className="flex w-full flex-col items-start justify-start gap-3 px-8 lg:w-1/2">
            <div className="font-helvetica flex w-full items-center justify-between gap-1 pb-4 text-xl font-bold text-black">
              <div>
                <p>Lesson Booking</p>
                <p>{lessonBookingDetails.lesson_name || "Lesson"}</p>
                <p>Instructor: {lessonBookingDetails.instructor_name || "Instructor"}</p>
                <p>{lessonBookingDetails.date}</p>
                <p>{lessonBookingDetails.time}</p>
              </div>
              <div className="flex flex-col items-center justify-center gap-2">
                <ClockFadingIcon className="text-primary" />
                <div className="justify-center">
                  <span className="font-helvetica text-xl leading-[25px] font-bold text-black">
                    {lessonBookingDetails.duration} min
                  </span>
                </div>
              </div>
            </div>
            {/* Addon extras selector - disabled for lessons */}
            {/* <AddonExtrasSelector /> */}
            {/* Payment method selector */}
            <PaymentMethodSelector
              selectedPaymentMethodId={selectedPaymentMethodId}
              onPaymentMethodChange={handlePaymentMethodChange}
              error={paymentMethodError}
              disabled={isProcessing}
              useWallet={useWallet}
              onWalletToggle={handleWalletToggle}
            />
          </div>
          <div className="z-1 hidden h-full w-[2px] rounded-full bg-[#EBEBEB] text-gray-300 lg:block"></div>
          <div className="flex w-full flex-col items-start justify-between px-8 lg:w-1/2">
            <LessonSummarySection />

            {/* Checkout Error */}
            {checkoutError && (
              <div className="mt-4">
                <ValidationError id="checkout-error" error={checkoutError} />
              </div>
            )}

            {/* Checkout Button */}
            <div className="w-full pt-4">
              {selectedPaymentMethodId === "APPLE_PAY" ? (
                <StripeProvider>
                  <div className="mt-6 w-full">
                    <InstructorApplePayButton
                      onBeforePayment={handleApplePayBeforePayment}
                      amount={Math.round(totalAmount * 100)} // Convert to cents
                      onPaymentSuccess={handleApplePaySuccess}
                    />
                  </div>
                </StripeProvider>
              ) : (
                <button
                  type="button"
                  onClick={handleCheckout}
                  disabled={!selectedPaymentMethodId || !checkoutData || isBooking || isProcessing}
                  className="bg-primary hover:bg-primary/90 w-full rounded-full px-4 py-3 font-medium text-white transition-colors disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {isBooking || isProcessing ? <Loader /> : "Complete Payment"}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LessonCheckout;
