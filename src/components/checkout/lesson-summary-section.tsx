import React, { useMemo } from "react";
import { useBookingStore } from "@/store/booking-store";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertCircle, RefreshCw } from "lucide-react";
import { useLessonBookingEstimate } from "@/hooks/use-booking-estimate";
import SummaryLineItem from "./summary-line-item";
import { useCheckoutStore } from "@/store/checkout-store";

// TypeScript interface for lesson booking estimate response
export interface LessonBookingEstimateData {
  tax_rate: string;
  booking_price: number;
  booking_price_tax: number;
  amount: number;
  tax_amount: number;
  total: number;
  due_amount: number;
  package_id: number;
  price: {
    id: number;
    user_id: number;
    lesson_type_id: number;
    package_id: number;
    type: string;
    cost: string;
    created_at: string;
    updated_at: string;
  };
  lesson_type: {
    id: number;
    name: string;
    min_duration: number;
    max_duration: number;
    default_duration: number;
    min_player: number;
    max_player: number;
    created_at: string;
    updated_at: string;
    cover_image: string;
    cancel_before_start_in_minutes: number;
    advance_minutes_in_minutes: number;
  };
}

export interface LessonBookingEstimateResponse {
  data: { status: string; message: string; data: LessonBookingEstimateData };
}

const LessonSummarySection: React.FC = () => {
  const { lessonBookingDetails } = useBookingStore();
  const { setTotalAmount } = useCheckoutStore();

  console.log("lessonBookingDetails from store", lessonBookingDetails);

  // Prepare lesson estimate request data
  const lessonEstimateRequestData = useMemo(() => {
    if (!lessonBookingDetails) return null;

    return {
      instructor_id: lessonBookingDetails.instructor_id,
      lesson_type_id: lessonBookingDetails.lesson_type_id,
      type: lessonBookingDetails.type,
      start_time: lessonBookingDetails.start_time,
      duration: lessonBookingDetails.duration,
      sub_court_id: lessonBookingDetails.sub_court_id,
    };
  }, [lessonBookingDetails]);

  // Use custom hook for lesson booking estimate
  const { estimateData, isLoading, error, refetch } =
    useLessonBookingEstimate(lessonEstimateRequestData);

  console.log("estimateData", estimateData);

  useMemo(() => {
    if (estimateData) {
      setTotalAmount(estimateData.total);
    }
  }, [estimateData, setTotalAmount]);

  if (!lessonBookingDetails) {
    return (
      <div className="w-full rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
        <div className="flex items-center justify-center py-8">
          <p className="font-helvetica text-sm text-gray-500">
            No lesson booking details available
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-white transition-all duration-200">
      {/* Header */}
      <div className="border-b border-gray-100 px-4 py-3 sm:px-6">
        <div className="flex items-center justify-between">
          <h3 className="font-helvetica text-lg font-semibold text-gray-900 sm:text-xl">
            Lesson Booking Summary
          </h3>
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-4 sm:px-6">
        {isLoading ? (
          <div className="space-y-3">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-center justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-16" />
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="w-full">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <p className="font-helvetica flex-1 text-sm text-red-700">{error}</p>
              <button
                onClick={refetch}
                className="flex items-center gap-1 rounded px-2 py-1 text-xs text-red-600 transition-colors hover:bg-red-100"
                disabled={isLoading}
              >
                <RefreshCw className={`h-3 w-3 ${isLoading ? "animate-spin" : ""}`} />
                Retry
              </button>
            </div>
          </div>
        ) : estimateData ? (
          <div className="space-y-2">
            {/* Base lesson booking */}
            <SummaryLineItem label="Lesson Booking" amount={estimateData.booking_price || 0} />

            {/* Package discount */}
            {estimateData.package_id > 0 && (
              <SummaryLineItem label="Package Applied" amount={0} isDiscount={false} />
            )}

            {/* Tax */}
            {estimateData.tax_amount > 0 && (
              <SummaryLineItem
                label={`Tax (${estimateData.tax_rate}%)`}
                amount={estimateData.tax_amount}
              />
            )}

            {/* Total */}
            <SummaryLineItem
              label="Total Amount"
              amount={estimateData.total}
              isTotal={true}
              className="border-t border-dashed border-gray-200 pt-2"
            />

            {/* Due Amount */}
            {estimateData.due_amount > 0 && (
              <SummaryLineItem
                label="Due Amount"
                amount={estimateData.due_amount}
                className="border-t border-dashed border-gray-200 pt-2"
              />
            )}

            {/* Show lesson type info */}
            {estimateData.lesson_type && (
              <div className="mt-3 rounded-md">
                <p className="font-helvetica text-primary text-sm font-medium">
                  {estimateData.lesson_type.name} - {estimateData.price?.type || "Hourly"} Rate
                </p>
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center justify-center py-4">
            <p className="font-helvetica text-sm text-gray-500">Loading estimate...</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default LessonSummarySection;
