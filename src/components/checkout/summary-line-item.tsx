import React from "react";

interface SummaryLineItemProps {
  label: string;
  amount: number;
  isDiscount?: boolean;
  isTotal?: boolean;
  className?: string;
}

const SummaryLineItem: React.FC<SummaryLineItemProps> = React.memo(
  ({ label, amount, isDiscount = false, isTotal = false, className = "" }) => (
    <div className={`flex items-center justify-between ${className}`}>
      <span
        className={`font-helvetica text-sm leading-relaxed sm:text-base ${
          isTotal ? "font-semibold text-black" : "font-normal text-gray-700"
        }`}
      >
        {label}
      </span>
      <span
        className={`font-helvetica text-right text-sm leading-relaxed sm:text-base ${
          isTotal
            ? "font-semibold text-black"
            : isDiscount
              ? "font-normal text-green-600"
              : "font-normal text-gray-900"
        }`}
      >
        {isDiscount && amount > 0 ? "-" : ""}${Math.abs(amount).toFixed(2)}
      </span>
    </div>
  )
);

SummaryLineItem.displayName = "SummaryLineItem";

export default SummaryLineItem;
