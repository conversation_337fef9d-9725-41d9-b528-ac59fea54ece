"use client";

import { useSubscriptionStore } from "@/store/subscription-store";
import { useRouter } from "next/navigation";
import MembershipStep from "../subscription/membership-step";

const MembershipSection = () => {
  const router = useRouter();
  const { currentStep, selectedPlan, setCurrentStep, setSelectedPlan } = useSubscriptionStore();

  const handleNext = () => {
    router.push("/subscription");
    console.log("handleNext");
    console.log("currentStep", currentStep);
    setCurrentStep(2);
  };

  return (
    <section id="">
      <div
        className="flex flex-col items-center justify-center overflow-hidden px-4 pt-4 font-bold"
        id="membership"
      >
        <div className="container px-4">
          <div className="mb-10 text-center">
            <h2 className="text-primary mb-2 text-4xl font-bold tracking-tight lg:text-6xl">
              Membership
            </h2>
            <h3 className="heading-5 color-900 text-lg md:text-xl">
              Become a Founding Member and join the Epic Padel Family
            </h3>
          </div>
        </div>
      </div>

      {/* Locations toggle button section */}
      <div className="font-helvetica mb-8 flex justify-center px-4 font-bold">
        <div className="relative flex w-full max-w-[480px] gap-2 rounded-full bg-gray-100 px-1 py-1 lg:gap-4">
          <div className="bg-primary cursor-pointer rounded-full px-4 py-2 text-center text-sm font-bold text-white shadow-md md:text-base lg:py-3">
            Charlotte, NC
          </div>
          <div className="relative flex cursor-not-allowed flex-col items-center justify-center text-sm opacity-50 md:text-base">
            <p className="text-center">Salt Lake City, UT</p>
            <p className="text-xs whitespace-nowrap text-gray-500">Coming Soon</p>
          </div>
          <div className="relative flex cursor-not-allowed flex-col items-center justify-center text-sm opacity-50 md:text-base">
            <span className="text-center">Tyson's Corner, VA</span>
            <span className="text-xs whitespace-nowrap text-gray-500">Coming Soon</span>
          </div>
        </div>
      </div>

      <div className="font-helvetica p-4">
        <MembershipStep
          selectedPlan={selectedPlan}
          onSelectPlan={setSelectedPlan}
          onNext={handleNext}
          isFromHome={true}
        />
      </div>
    </section>
  );
};

export default MembershipSection;
