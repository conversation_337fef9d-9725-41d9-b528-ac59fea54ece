import { useGetPackages } from "@/api/package-service";
import { Card, CardContent } from "@/components/ui/card";
import { Plan } from "@/types/membership";
import { Check, Users } from "lucide-react";
import { useState } from "react";

interface MembershipStepProps {
  selectedPlan: Plan | null;
  onSelectPlan: (plan: Plan) => void;
  onNext: () => void;
  isFromHome?: boolean;
}

export default function MembershipStep({
  selectedPlan,
  onSelectPlan,
  onNext,
  isFromHome = false,
}: MembershipStepProps) {
  const [billingPeriod, setBillingPeriod] = useState<"monthly" | "annually">("monthly");

  const { packageList, packageListLoading } = useGetPackages();

  // Calculate pricing based on billing period
  const getDisplayPrice = (plan: Plan) => {
    const priceObj =
      billingPeriod === "monthly"
        ? plan.package_prices.find((p) => p.frequency === "M")
        : plan.package_prices.find((p) => p.frequency === "Y");

    return priceObj ? priceObj.amount : 0;
  };

  const getDisplayPeriod = (plan: Plan) => {
    if (plan.name === "Pay-to-Play") return "90 min session";
    if (billingPeriod === "annually") return "year";
    return "month";
  };

  // Check if billing toggle should be shown (only if there are monthly plans)
  const hasMonthlyPlans = packageList.some((plan) =>
    plan.package_prices.some((price) => price.frequency === "M")
  );

  const handleSelectPlan = (plan: Plan) => {
    onSelectPlan(plan);
    // Automatically proceed to next step after selecting a plan
    setTimeout(() => {
      onNext();
    }, 300); // Small delay for visual feedback
  };

  // Helper functions for plan properties
  const isFoundingPlan = (plan: Plan) => {
    return plan.id === 2 || plan.package_prices.some((p) => p.is_on_offer);
  };

  const getSpotsRemaining = (plan: Plan) => {
    const priceObj = plan.package_prices.find((p) => p.frequency === "M");
    return priceObj?.limited_members || 32;
  };

  if (packageListLoading) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-[#1C5534]"></div>
          <p className="text-gray-600">Loading membership plans...</p>
        </div>
      </div>
    );
  }

  if (packageList.length === 0) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="text-center">
          <p className="text-gray-600">No membership plans available at the moment.</p>
          <p className="mt-2 text-sm text-gray-500">Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center">
      {!isFromHome && (
        <div className="mb-12 px-6 text-center">
          <h2 className="mb-2 text-gray-900">Choose Your Membership</h2>
          <p className="text-lg text-gray-600">
            Select the perfect plan for your journey with Epic Charlotte
          </p>
        </div>
      )}

      {/* Billing Toggle - Only show if there are monthly plans */}
      {hasMonthlyPlans && (
        <div className="mb-8 flex justify-center">
          <div className="flex items-center rounded-full bg-gray-100 p-1">
            <button
              onClick={() => setBillingPeriod("monthly")}
              className={`rounded-full px-6 py-2 text-sm transition-all duration-200 ${
                billingPeriod === "monthly"
                  ? "bg-primary font-medium text-white shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingPeriod("annually")}
              className={`rounded-full px-6 py-2 text-sm transition-all duration-200 ${
                billingPeriod === "annually"
                  ? "bg-primary font-medium text-white shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              <span>Annual</span>
              <span className="ml-2 rounded-full bg-[#DEBA0C] px-2 py-0.5 text-xs text-white">
                Save 5%
              </span>
            </button>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 gap-6 pt-8 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
        {packageList.map((plan) => {
          const isPopular = isFoundingPlan(plan);
          const spotsRemaining = getSpotsRemaining(plan);

          return (
            <Card
              key={plan.id}
              className={`group relative cursor-pointer overflow-hidden transition-all duration-300 hover:-translate-y-1 hover:shadow-2xl ${
                selectedPlan?.id === plan.id
                  ? "scale-[1.02] shadow-2xl ring-2 ring-[#1C5534]"
                  : "hover:shadow-xl"
              } ${isPopular ? "shadow-xl ring-2 ring-[#DEBA0C]" : ""}`}
              onClick={(e) => {
                // Prevent card click when button is clicked
                if (!(e.target as HTMLElement).closest("button")) {
                  onSelectPlan(plan);
                }
              }}
            >
              {/* Background gradient overlay */}
              <div
                className={`absolute inset-0 bg-gradient-to-br opacity-5 transition-opacity duration-300 ${
                  isPopular
                    ? "from-[#DEBA0C] to-[#B8940A] group-hover:opacity-10"
                    : selectedPlan?.id === plan.id
                      ? "from-[#1C5534] to-green-700 group-hover:opacity-10"
                      : "from-gray-400 to-gray-600 group-hover:opacity-8"
                }`}
              />

              <CardContent className="relative z-10 h-full p-8">
                {/* Selection indicator */}
                {selectedPlan?.id === plan.id && (
                  <div className="absolute top-3 right-3 z-20">
                    <div
                      className={`flex h-6 w-6 items-center justify-center rounded-full ${isPopular ? "bg-[#DEBA0C]" : "bg-[#1C5534]"} shadow-lg`}
                    >
                      <Check className="h-4 w-4 text-white" />
                    </div>
                  </div>
                )}

                <div className="flex h-full flex-col justify-between space-y-6">
                  <div className="">
                    {/* Header */}
                    <div className="space-y-2 text-center">
                      <h3 className="text-xl font-semibold text-gray-900">{plan.name}</h3>
                    </div>

                    {/* Pricing */}
                    <div className="py-4 text-center">
                      {/* Hide pricing for Pay-to-Play plan */}
                      {plan.name !== "Pay-to-Play" && (
                        <>
                          {/* Show crossed out original price only for founding membership */}
                          {isPopular && billingPeriod === "monthly" && (
                            <div className="mb-2">
                              <span className="text-lg text-gray-500 line-through">
                                $279/{getDisplayPeriod(plan)}
                              </span>
                            </div>
                          )}
                          <div className="flex items-baseline justify-center space-x-2">
                            <span className="text-4xl font-bold text-gray-900">
                              ${getDisplayPrice(plan)}
                            </span>
                            <span className="text-lg text-gray-500">/{getDisplayPeriod(plan)}</span>
                          </div>
                          {/* Show monthly equivalent for annual billing */}
                          {billingPeriod === "annually" && (
                            <div className="mt-2">
                              <span className="text-sm text-gray-500">
                                ${Math.round(getDisplayPrice(plan) / 12)}/month when billed annually
                              </span>
                            </div>
                          )}
                          {/* Promotional pricing text for founding membership */}
                          {isPopular && billingPeriod === "monthly" && (
                            <div className="mt-3">
                              <span className="text-sm font-medium text-gray-600">
                                $0 Initiation Fee • $149/{getDisplayPeriod(plan)} for First 3 Months
                              </span>
                            </div>
                          )}
                        </>
                      )}
                    </div>

                    {/* Promotional Banner - Only for Founding Membership */}
                    {isPopular && (
                      <div className="mb-4">
                        <div className="rounded-lg bg-gradient-to-r from-[#DEBA0C] to-[#B8940A] px-4 py-2 text-center text-white shadow-lg">
                          <p className="text-xs font-bold">
                            🎉 50% OFF Founding Membership - Only {spotsRemaining || 32} Spots Left!
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Spots remaining */}
                    {spotsRemaining && isPopular && (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between text-sm">
                          <span className="flex items-center text-gray-600">
                            <Users className="mr-2 h-4 w-4" />
                            Spots Available
                          </span>
                          <span className="font-semibold text-[#B8940A]">
                            {spotsRemaining} of 50 left
                          </span>
                        </div>
                        <div className="h-2 w-full rounded-full bg-gray-200">
                          <div
                            className="h-2 rounded-full bg-gradient-to-r from-[#DEBA0C] to-[#B8940A] transition-all duration-500"
                            style={{ width: `${(spotsRemaining / 50) * 100}%` }}
                          />
                        </div>
                        <p className="text-center text-xs font-medium text-[#B8940A]">
                          Limited Time - Founding Member Pricing
                        </p>
                      </div>
                    )}

                    {/* Features */}
                    <div className="space-y-4">
                      <div className="border-t border-gray-100 pt-6">
                        <h4 className="mb-4 text-center font-semibold text-gray-900">
                          What's Included
                        </h4>
                        {plan.benefit_details ? (
                          <div
                            className="plan-benefit-details text-sm"
                            dangerouslySetInnerHTML={{ __html: plan.benefit_details }}
                          />
                        ) : (
                          <ul className="space-y-3">
                            {plan.package_benefits.map((benefit) => (
                              <li key={benefit.id} className="flex items-start text-gray-600">
                                <div className="mt-0.5 mr-3 flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-green-100">
                                  <Check className="h-3 w-3 text-green-600" />
                                </div>
                                <span className="text-sm leading-relaxed">{benefit.name}</span>
                              </li>
                            ))}
                          </ul>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Action area */}
                  <div className="pt-4">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSelectPlan(plan);
                      }}
                      disabled={plan?.is_default}
                      className={`w-full rounded-full px-4 py-3 text-center transition-all duration-200 ${
                        plan?.is_default
                          ? "cursor-not-allowed bg-gray-400 text-gray-600" // Pay-to-Play - disabled style
                          : isPopular
                            ? "bg-gradient-to-r from-[#DEBA0C] to-[#B8960A] text-white shadow-lg hover:scale-105 hover:from-[#B8960A] hover:to-[#DEBA0C] active:scale-95" // Founding Member - gold gradient
                            : selectedPlan?.id === plan.id
                              ? "bg-[#1C5534] text-white shadow-lg hover:scale-105 hover:bg-[#0f3a1f] active:scale-95" // Selected - green
                              : "bg-[#1C5534] text-white shadow-lg hover:scale-105 hover:bg-[#0f3a1f] active:scale-95" // Other plans - green
                      }`}
                    >
                      <span className="font-semibold">
                        {plan?.is_default
                          ? "Default Plan"
                          : selectedPlan?.id === plan.id
                            ? "Selected - Continue"
                            : "Select Plan"}
                      </span>
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Additional info */}
      <div className="mt-12 text-center">
        <p className="text-sm text-gray-500">
          All memberships include a 30-day money-back guarantee. Cancel anytime.
        </p>
      </div>
    </div>
  );
}
