import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ExternalLink } from "lucide-react";
import TermsAndConditionsContent from "../term-and-conditions-content";

const TermAndConditionsDialog = () => {
  const [showWaiver, setShowWaiver] = useState(false);
  return (
    <Dialog open={showWaiver} onOpenChange={setShowWaiver}>
      <DialogTrigger asChild>
        <button
          type="button"
          className="inline-flex cursor-pointer items-center pt-2 font-medium text-[#1C5534] underline transition-colors hover:text-[#0f3a1f]"
          onClick={(e) => {
            e.preventDefault();
            setShowWaiver(true);
          }}
        >
          EPIC PADEL INC. LIABILITY WAIVER AND RELEASE AGREEMENT
          <ExternalLink className="ml-1 h-3 w-3" />
        </button>
      </DialogTrigger>
      <DialogContent className="max-h-[80vh] w-[96%] max-w-2xl overflow-y-auto rounded-lg bg-white">
        <DialogHeader>
          <DialogTitle className="text-lg">
            EPIC PADEL INC. LIABILITY WAIVER AND RELEASE AGREEMENT
          </DialogTitle>
          <DialogDescription className="sr-only">
            Legal liability waiver and release agreement that users must read and accept
          </DialogDescription>
        </DialogHeader>

        <TermsAndConditionsContent />
      </DialogContent>
    </Dialog>
  );
};

export default TermAndConditionsDialog;
