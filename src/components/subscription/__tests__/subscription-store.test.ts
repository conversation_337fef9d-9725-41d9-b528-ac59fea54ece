import { renderHook, act } from '@testing-library/react';
import { useSubscriptionStore } from '@/store/subscription-store';
import { Plan } from '@/types/membership';
import { PersonalInfo } from '../types';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock as any;

describe('useSubscriptionStore', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useSubscriptionStore());

    expect(result.current.selectedPlan).toBeNull();
    expect(result.current.personalInfo).toEqual({
      name: "",
      mobile: "",
      email: "",
      hasAgreedToWaiver: false,
      isAbove18: false,
    });
    expect(result.current.selectedPaymentMethod).toBeNull();
    expect(result.current.currentStep).toBe(1);
    expect(result.current.isProcessing).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('should set selected plan', () => {
    const { result } = renderHook(() => useSubscriptionStore());
    const mockPlan: Plan = {
      id: 1,
      name: 'Test Plan',
      description: 'Test Description',
      package_prices: [{ id: 1, frequency: 'M', amount: 100 }],
      package_benefits: [],
      one_time_price: '0',
      is_default: false,
    };

    act(() => {
      result.current.setSelectedPlan(mockPlan);
    });

    expect(result.current.selectedPlan).toEqual(mockPlan);
    expect(result.current.error).toBeNull();
  });

  it('should update personal info', () => {
    const { result } = renderHook(() => useSubscriptionStore());
    const updates: Partial<PersonalInfo> = {
      name: 'John Doe',
      email: '<EMAIL>',
    };

    act(() => {
      result.current.updatePersonalInfo(updates);
    });

    expect(result.current.personalInfo.name).toBe('John Doe');
    expect(result.current.personalInfo.email).toBe('<EMAIL>');
    expect(result.current.personalInfo.mobile).toBe(''); // unchanged
  });

  it('should validate step progression correctly', () => {
    const { result } = renderHook(() => useSubscriptionStore());

    // Step 1: Should not proceed without selected plan
    expect(result.current.canProceedToNextStep()).toBe(false);

    // Add plan
    const mockPlan: Plan = {
      id: 1,
      name: 'Test Plan',
      description: 'Test Description',
      package_prices: [{ id: 1, frequency: 'M', amount: 100 }],
      package_benefits: [],
      one_time_price: '0',
      is_default: false,
    };

    act(() => {
      result.current.setSelectedPlan(mockPlan);
    });

    expect(result.current.canProceedToNextStep()).toBe(true);

    // Move to step 2
    act(() => {
      result.current.setCurrentStep(2);
    });

    // Step 2: Should not proceed without complete personal info
    expect(result.current.canProceedToNextStep()).toBe(false);

    // Complete personal info
    act(() => {
      result.current.setPersonalInfo({
        name: 'John Doe',
        mobile: '+1234567890',
        email: '<EMAIL>',
        hasAgreedToWaiver: true,
        isAbove18: true,
      });
    });

    expect(result.current.canProceedToNextStep()).toBe(true);
  });

  it('should populate user data correctly', () => {
    const { result } = renderHook(() => useSubscriptionStore());

    act(() => {
      result.current.populateUserData({
        name: 'Jane Doe',
        email: '<EMAIL>',
        phone: '+9876543210',
      });
    });

    expect(result.current.personalInfo.name).toBe('Jane Doe');
    expect(result.current.personalInfo.email).toBe('<EMAIL>');
    expect(result.current.personalInfo.mobile).toBe('+9876543210');
  });

  it('should not overwrite existing personal info when populating user data', () => {
    const { result } = renderHook(() => useSubscriptionStore());

    // Set existing data
    act(() => {
      result.current.setPersonalInfo({
        name: 'Existing Name',
        mobile: 'Existing Mobile',
        email: '<EMAIL>',
        hasAgreedToWaiver: false,
        isAbove18: false,
      });
    });

    // Try to populate with new data
    act(() => {
      result.current.populateUserData({
        name: 'New Name',
        email: '<EMAIL>',
        phone: 'New Phone',
      });
    });

    // Should keep existing data
    expect(result.current.personalInfo.name).toBe('Existing Name');
    expect(result.current.personalInfo.email).toBe('<EMAIL>');
    expect(result.current.personalInfo.mobile).toBe('Existing Mobile');
  });

  it('should reset subscription correctly', () => {
    const { result } = renderHook(() => useSubscriptionStore());
    const mockPlan: Plan = {
      id: 1,
      name: 'Test Plan',
      description: 'Test Description',
      package_prices: [{ id: 1, frequency: 'M', amount: 100 }],
      package_benefits: [],
      one_time_price: '0',
      is_default: false,
    };

    // Set some data
    act(() => {
      result.current.setSelectedPlan(mockPlan);
      result.current.setCurrentStep(3);
      result.current.setError('Test error');
    });

    // Reset
    act(() => {
      result.current.resetSubscription();
    });

    expect(result.current.selectedPlan).toBeNull();
    expect(result.current.currentStep).toBe(1);
    expect(result.current.error).toBeNull();
    expect(result.current.personalInfo).toEqual({
      name: "",
      mobile: "",
      email: "",
      hasAgreedToWaiver: false,
      isAbove18: false,
    });
  });
});
