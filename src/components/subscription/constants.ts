import { MembershipPlan, PaymentMethod, Step } from './types'

export const steps: Step[] = [
  { id: 1, title: 'Membership', description: 'Choose your plan' },
  { id: 2, title: 'Personal Info', description: 'Your details' },
  { id: 3, title: 'Checkout', description: 'Complete purchase' },
  { id: 4, title: 'Success', description: 'Welcome aboard!' }
]

export const membershipPlans: MembershipPlan[] = [
  {
    id: 'founding',
    name: 'Founding Membership',
    price: 149,
    period: 'month',
    description: 'Epic Charlotte',
    features: [
      'Book courts anytime - ✪All-Inclusive Court Access✪, no limits (even peak hours!)',
      'Reserve courts up to 14 days early',
      'Enjoy free rentals — padel racquet and balls on us',
      'Bring 2 guests per month — share the Epic experience',
      'Get 15% off lessons, clinics, and tournaments',
      'Unlock access to exclusive events, challenges, and member perks',
      'Get your free Epic Welcome Pack — only for founding members'
    ],
    isPopular: true,
    spotsRemaining: 32,
    totalSpots: 50
  },
  {
    id: 'premium',
    name: 'Epic Platinum',
    price: 109,
    period: 'month',
    description: 'Premium membership benefits',
    features: [
      'Unlimited court access during off-peak hours',
      '4 peak-time sessions per month included',
      'Book courts up to 10 days in advance',
      '10% off lessons, clinics, and tournaments',
      '2 guest passes per month',
      'Priority access to member events and tournaments'
    ]
  },
  {
    id: 'pro',
    name: 'Epic Junior',
    price: 79,
    period: 'month',
    description: 'Perfect for young players',
    features: [
      '1 90-minute court session per day (off-peak only)',
      '1 peak-time session per week allowed',
      'Extra court sessions (if needed): $48 per 90 minutes (off-peak) $72 per 90 minutes (peak)',
      'Book courts up to 5 days in advance',
      '5% off private lessons and junior clinics',
      'Access to junior tournaments and development programs'
    ]
  },
  {
    id: 'basic',
    name: 'Corporate Membership',
    price: 500,
    period: 'month',
    description: 'Perfect for businesses',
    features: [
      '2 peak-time bookings per week',
      'Discounted event hosting rates',
      'Free introductory lesson for employees',
      'Access to exclusive business networking events'
    ]
  },
  {
    id: 'starter',
    name: 'Pay-to-Play',
    price: 80,
    period: 'session',
    description: 'No commitment required',
    features: [
      'Pay per court session: $80 for 90 minutes (off-peak) $140 for 90 minutes (peak)',
      'Book courts up to 24 hours in advance',
      'No ongoing commitment — just book and play',
      'Cost per player (based on 4 players): Off-peak: $20/player Peak: $35/player'
    ]
  }
]

export const existingPaymentMethods: PaymentMethod[] = [
  {
    id: '1',
    type: 'card',
    last4: '4242',
    brand: 'Visa',
    isDefault: true
  },
  {
    id: '2',
    type: 'card',
    last4: '5555',
    brand: 'Mastercard',
    isDefault: false
  }
]