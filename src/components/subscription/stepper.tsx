import React from "react";
import { Check, ChevronRight } from "lucide-react";
import { Step } from "./types";

interface StepperProps {
  steps: Step[];
  currentStep: number;
  onStepClick?: (stepId: number) => void;
}

export default function Stepper({ steps, currentStep, onStepClick }: StepperProps) {
  const handleStepClick = (stepId: number) => {
    // Only allow clicking on completed steps (steps that have been passed)
    if (currentStep > stepId && onStepClick) {
      onStepClick(stepId);
    }
  };

  return (
    <div className="mb-6 md:mb-8">
      <div className="flex items-center justify-center space-x-2 sm:space-x-4 md:space-x-8">
        {steps.map((step, index) => {
          const isCompleted = currentStep > step.id;
          const isClickable = isCompleted && onStepClick;

          return (
            <div key={step.id} className="flex items-center">
              <div
                className={`flex flex-col items-center transition-colors duration-200 ${isClickable ? "cursor-pointer" : ""}`}
                onClick={() => handleStepClick(step.id)}
              >
                <div
                  className={`flex h-8 w-8 items-center justify-center rounded-full border-2 transition-all duration-200 sm:h-10 sm:w-10 md:h-12 md:w-12 ${
                    currentStep >= step.id
                      ? "border-[#1C5534] bg-[#1C5534] text-white"
                      : "border-gray-300 bg-white text-gray-400"
                  } ${isClickable ? "hover:border-[#0f3a1f] hover:bg-[#0f3a1f]" : ""}`}
                >
                  {currentStep > step.id ? (
                    <Check className="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5" />
                  ) : (
                    <span className="text-xs font-medium sm:text-sm md:text-base">{step.id}</span>
                  )}
                </div>
                <div className="mt-1 line-clamp-1 max-w-[70px] text-center sm:mt-2 sm:max-w-[80px] md:line-clamp-2 md:max-w-none">
                  <p
                    className={`text-xs font-medium transition-colors duration-200 sm:text-sm md:text-base ${
                      currentStep >= step.id ? "text-[#1C5534]" : "text-gray-500"
                    } ${isClickable ? "hover:text-[#0f3a1f]" : ""}`}
                  >
                    {step.title}
                  </p>
                  <p className="hidden text-[10px] text-gray-400 sm:block sm:text-xs">
                    {step.description}
                  </p>
                </div>
              </div>
              {index < steps.length - 1 && (
                <ChevronRight className="mx-1 mt-[-12px] h-3 w-3 text-gray-300 sm:mx-2 sm:mt-[-16px] sm:h-4 sm:w-4 md:mx-4 md:mt-[-24px] md:h-5 md:w-5" />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
