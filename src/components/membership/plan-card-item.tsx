import { Plan } from "@/types/membership";

// PlanCard Component
interface PlanCardItemProps {
  plan: Plan;
  price: number;
  isYearly: boolean;
  founding: boolean;
  initialAmount: number;
  iterationOffer: number;
  actualAmount: number;
  limitedMembers: number;
  foundingMonthlyPriceInAnnual: number;
  foundingAnnualActualAmount: number;
  foundingAnnualItterationOffer: number;
  isUserHavePlan?: number;
  onSubscribe: (plan: Plan, price: number) => void;
}

const PlanCardItem: React.FC<PlanCardItemProps> = ({
  plan,
  price,
  isYearly,
  founding,
  initialAmount,
  iterationOffer,
  actualAmount,
  limitedMembers,
  foundingMonthlyPriceInAnnual,
  foundingAnnualActualAmount,
  foundingAnnualItterationOffer,
  isUserHavePlan,
  onSubscribe,
}) => {
  const getButtonText = () => {
    if (plan.is_default) return "Default Plan";
    if (isUserHavePlan === plan.id) return "Current Plan";
    return "Join Now";
  };

  const getButtonStyles = () => {
    if (plan.is_default) {
      return {
        cursor: "not-allowed",
        backgroundColor: "#7B7B7B",
        opacity: "1",
        pointerEvents: "none" as const,
      };
    }
    if (isUserHavePlan === plan.id) {
      return {
        cursor: "not-allowed",
        background: "linear-gradient(260deg,rgba(221, 186, 10, 1) 0%,rgba(255, 238, 150, 1) 100%)",
        opacity: "1",
        pointerEvents: "none" as const,
      };
    }
    return {};
  };

  const getPriceText = () => {
    return typeof price === "number"
      ? isYearly
        ? `$${founding ? initialAmount.toFixed(0) : price.toFixed(0)}`
        : `$${founding ? foundingMonthlyPriceInAnnual.toFixed(0) : (price / 12).toFixed(0)}`
      : "N/A";
  };

  const cardContent = (
    <div
      className={`flex max-w-[320px] flex-col rounded-3xl bg-white p-5 text-black shadow-md md:max-w-[260px] ${founding ? "mt-0" : "border-primary mt-9 border-[0.5px]"} `}
      style={founding && window.innerWidth > 800 ? { height: "96.2%" } : {}}
    >
      {/* Plan Name */}
      <div className="mb-4 w-full rounded-md bg-blue-50 px-2 py-2 text-center text-sm font-bold text-gray-800">
        {plan.name}
      </div>

      {/* Pricing Section */}
      {plan.is_default ? (
        <div className="font-helvetica mb-2 flex items-center justify-start gap-2">
          <h4 className="pt-2.5 pb-0 text-3xl font-bold">{plan.name}</h4>
        </div>
      ) : (
        <div className="mb-2">
          {founding && (
            <p className="pt-2 text-sm leading-tight text-gray-500 line-through">
              ${isYearly ? actualAmount : foundingAnnualActualAmount}
            </p>
          )}
          <div className="flex items-center justify-start gap-1.5">
            <h4 className={`font-helvetica pb-0 text-3xl font-bold ${founding ? "pt-0" : "pt-2"}`}>
              {getPriceText()}
            </h4>
            <span className="text-gray-600">/ month </span>
          </div>
        </div>
      )}

      {/* Founding Member Details */}
      {founding && (
        <>
          <p className="font-helvetica pb-2 leading-tight font-bold">
            50% OFF Founding Membership - Only 32 Spots Left!
          </p>
          <p className="font-helvetica text-sm text-gray-500">
            {`$0 Initiation Fee . ${getPriceText()}/month for First ${isYearly ? iterationOffer : foundingAnnualItterationOffer}  Months`}
          </p>
          <p className="font-helvetica text-sm font-bold text-[#DDBA0A]">
            ✪ Zero Court Booking Fees ✪
          </p>
        </>
      )}

      {/* Initiation Fee */}
      <div className="mb-6">
        {!founding && (
          <span className="font-helvetica text-sm text-gray-700">
            + ${parseInt(plan.one_time_price, 10)} one time initiation fee
          </span>
        )}
      </div>

      {/* Benefits List */}
      {/* <ul className="mb-6 flex-1 space-y-2">
        {plan.package_benefits.map((benefit, index) => (
          <li
            key={benefit.id}
            className="flex items-start gap-2"
            style={
              founding && index === 0
                ? {
                    background:
                      "linear-gradient(260deg, rgba(221, 186, 10, 1) 0%, rgba(255, 238, 150, 1) 100%)",
                    borderRadius: "5px",
                    padding: "4px",
                  }
                : {}
            }
          >
            <Check width={16} height={16} className="mt-0.5 flex-shrink-0 text-green-600" />
            <span className="text-sm text-gray-800">{benefit.name}</span>
          </li>
        ))}
      </ul> */}

      {/* Benefits List v2*/}
      {plan?.benefit_details ? (
        <div
          className="plan-benefit-details mb-6 flex-1"
          dangerouslySetInnerHTML={{ __html: plan.benefit_details }}
        ></div>
      ) : (
        <div className="mb-6 flex-1"></div>
      )}

      {/* Subscribe Button */}
      <button
        onClick={() => onSubscribe(plan, price)}
        disabled={plan.is_default || isUserHavePlan === plan.id}
        style={getButtonStyles()}
        className={`bg-primary font-helvetica w-full cursor-pointer rounded-full border-none px-6 py-4 text-base font-bold text-white transition-all duration-200 hover:scale-105 ${
          plan.is_default || isUserHavePlan === plan.id
            ? "cursor-not-allowed opacity-100"
            : "hover:bg-primary/90"
        } `}
      >
        {getButtonText()}
      </button>
    </div>
  );

  if (founding) {
    return (
      <div
        className="rounded-3xl px-0.5 pt-2.5 pb-0.5"
        style={{
          background:
            "linear-gradient(260deg, rgba(221, 186, 10, 1) 0%, rgba(255, 238, 150, 1) 100%)",
        }}
      >
        <p className="mb-2 text-center text-lg leading-none font-semibold text-white">
          32 of {limitedMembers} spots remaining
        </p>
        {cardContent}
      </div>
    );
  }

  return cardContent;
};

export default PlanCardItem;
