import { useBookingStore } from "@/store/booking-store";
import React from "react";

export const LocationSelector = () => {
  const locations = [
    {
      name: "Charlotte, NC",
      id: "charlotte",
      comingSoon: false,
    },
    {
      name: "Salt Lake City, UT",
      id: "utah",
      comingSoon: true,
    },
    {
      name: "Tyson's Corner, VA",
      id: "tyson",
      comingSoon: true,
    },
  ];

  const { selectedLocation, setSelectedLocation } = useBookingStore();

  const handleLocationClick = (location: string) => {
    setSelectedLocation(location);
  };

  return (
    <div className="container flex w-full items-center justify-center px-4 pt-6">
      <div className="font-helvetica flex justify-center rounded-full p-2.5 px-4 font-bold outline-1 outline-offset-[-1px] outline-[#c3c3c3]">
        <div className="relative flex w-full max-w-[500px] gap-2 rounded-full bg-transparent lg:gap-4">
          <div className="cursor-pointer rounded-full bg-[#ddba0a] px-4 py-2 text-center text-sm font-bold text-[#1c5534] shadow-md md:text-base lg:py-3">
            Charlotte, NC
          </div>
          <div className="relative flex cursor-not-allowed flex-col items-center justify-center text-sm opacity-50 md:text-base">
            <p className="text-center">Salt Lake City, UT</p>
            <p className="text-xs whitespace-nowrap text-gray-500">Coming Soon</p>
          </div>
          <div className="relative flex cursor-not-allowed flex-col items-center justify-center text-sm opacity-50 md:text-base">
            <span className="text-center">Tyson's Corner, VA</span>
            <span className="text-xs whitespace-nowrap text-gray-500">Coming Soon</span>
          </div>
        </div>
      </div>
    </div>
  );
};
