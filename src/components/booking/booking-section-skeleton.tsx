import React from "react";
import { Skeleton } from "@/components/ui/skeleton";

const BookingSectionSkeleton = () => {
  return (
    <div className="flex w-full flex-col items-center justify-center space-y-6 py-8">
      {/* Sport Selector Skeleton */}
      <div className="container flex w-full items-center justify-start gap-2 py-4 sm:gap-6 md:gap-10 md:py-8">
        {[1, 2, 3].map((index) => (
          <div
            key={index}
            className="relative flex h-16 flex-1 items-center justify-center gap-2.5 rounded-[10px] p-2 sm:h-20 md:h-[92px] md:p-2.5"
          >
            <Skeleton className="absolute inset-0 rounded-[10px]" />
            <div className="relative z-10 text-center">
              <Skeleton className="h-6 w-16 sm:h-7 sm:w-20 md:h-8 md:w-24" />
              {index > 1 && <Skeleton className="mt-1 h-3 w-20 sm:h-4 sm:w-24" />}
            </div>
          </div>
        ))}
      </div>

      {/* Booking Type Selector Skeleton */}
      <div className="container w-full">
        {/* Tab List Skeleton */}
        <div className="h-[68px] w-full rounded-[100px] bg-neutral-100 p-1">
          <div className="flex h-full gap-1">
            {["Book a Court", "Programs", "Lessons", "Instructors"].map((tab, index) => (
              <Skeleton
                key={tab}
                className={`h-full flex-1 rounded-[30px] ${
                  index === 0 ? "bg-[#1c5534]" : "bg-transparent"
                }`}
              />
            ))}
          </div>
        </div>

        {/* Tab Content Skeleton */}
        <div className="mt-6">
          {/* Grid of content cards skeleton */}
          <div className="grid grid-cols-1 items-center justify-items-center gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {Array.from({ length: 6 }).map((_, index) => (
              <div
                key={index}
                className="inline-flex w-full max-w-[350px] flex-col items-center justify-start gap-4 rounded-[20px] bg-white p-5 shadow-[0px_0px_15px_0px_rgba(0,0,0,0.08)]"
              >
                {/* Card image/header skeleton */}
                <Skeleton className="h-16 w-full rounded-[10px]" />

                {/* Card content skeleton */}
                <div className="w-full space-y-2">
                  <Skeleton className="h-5 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>

                {/* Card button skeleton */}
                <Skeleton className="h-12 w-full rounded-[30px]" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingSectionSkeleton;
