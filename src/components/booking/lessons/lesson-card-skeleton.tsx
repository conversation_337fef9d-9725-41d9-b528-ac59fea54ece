import React from "react";
import { Skeleton } from "@/components/ui/skeleton";

const LessonCardSkeleton = () => {
  return (
    <div className="inline-flex w-[300px] flex-col items-center justify-start gap-[15px] rounded-[20px] bg-white px-5 py-5 shadow-[0px_0px_15px_0px_rgba(0,0,0,0.08)] outline outline-[#ddba0a]">
      {/* Cover image skeleton */}
      <Skeleton className="h-[79px] w-full rounded-[10px]" />

      {/* Focus areas skeleton */}
      <div className="w-full space-y-1">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
      </div>

      {/* Button skeleton */}
      <Skeleton className="h-12 w-full rounded-[30px]" />
    </div>
  );
};

export default LessonCardSkeleton;
