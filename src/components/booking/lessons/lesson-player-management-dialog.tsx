import React, { useState, useCallback } from "react";
import * as Dialog from "@radix-ui/react-dialog";
import { X, Plus, ArrowLeft } from "lucide-react";
import { addPlayer } from "@/api/booking-checkout-service";
import { toast } from "sonner";
import Loader from "../../loader";

// Player interface for lesson participants
interface Player {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  country_code?: string;
  isSelected: boolean;
}

// Interface for adding new player
interface NewPlayerData {
  name: string;
  email: string;
  country_code: string;
  phone: string;
}

interface LessonPlayerManagementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  players: Player[];
  onPlayerToggle: (playerId: string) => void;
  selectedPlayersCount: number;
  maxPlayers: number;
  playerListLoading: boolean;
  revalidateGetPlayerList: () => void;
  onPlayerAdded: () => void;
}

type DialogMode = "select" | "add";

const LessonPlayerManagementDialog: React.FC<LessonPlayerManagementDialogProps> = ({
  isOpen,
  onClose,
  players,
  onPlayerToggle,
  selectedPlayersCount,
  maxPlayers,
  playerListLoading,
  revalidateGetPlayerList,
  onPlayerAdded,
}) => {
  const [dialogMode, setDialogMode] = useState<DialogMode>("select");
  const [isAddingPlayer, setIsAddingPlayer] = useState(false);

  console.log("no of max players", maxPlayers);

  // New player form data
  const [newPlayerData, setNewPlayerData] = useState<NewPlayerData>({
    name: "",
    email: "",
    country_code: "+1",
    phone: "",
  });

  // Handle switching to add player mode
  const handleSwitchToAddMode = useCallback(() => {
    setDialogMode("add");
  }, []);

  // Handle switching back to select mode
  const handleSwitchToSelectMode = useCallback(() => {
    setDialogMode("select");
    // Reset form data when going back
    setNewPlayerData({
      name: "",
      email: "",
      country_code: "+1",
      phone: "",
    });
  }, []);

  // Handle adding new player
  const handleAddPlayer = useCallback(async () => {
    if (!newPlayerData.name || !newPlayerData.email || !newPlayerData.phone) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsAddingPlayer(true);
    try {
      await addPlayer(newPlayerData);
      toast.success("Participant added successfully");

      // Revalidate the player list to get updated data
      revalidateGetPlayerList();

      // Reset form and switch back to select mode
      setNewPlayerData({
        name: "",
        email: "",
        country_code: "+1",
        phone: "",
      });
      setDialogMode("select");

      // Notify parent component that player was added
      onPlayerAdded();
    } catch (error: any) {
      console.error("Failed to add participant:", error);
      toast.error(error?.message || "Failed to add participant");
    } finally {
      setIsAddingPlayer(false);
    }
  }, [newPlayerData, revalidateGetPlayerList, onPlayerAdded]);

  // Handle dialog close
  const handleClose = useCallback(() => {
    setDialogMode("select");
    setNewPlayerData({
      name: "",
      email: "",
      country_code: "+1",
      phone: "",
    });
    revalidateGetPlayerList();
    onClose();
  }, [onClose]);

  return (
    <Dialog.Root open={isOpen} onOpenChange={handleClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[2000] bg-black/50" />
        <Dialog.Content className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-[2001] w-full max-w-lg translate-x-[-50%] translate-y-[-50%] rounded-[20px] bg-white p-5 shadow-lg duration-200">
          <Dialog.Close className="focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-2 right-2 rounded-full opacity-70 ring-0 transition-opacity hover:opacity-100 focus:ring-0 focus:outline-none disabled:pointer-events-none">
            <X className="h-8 w-8 text-gray-600 outline-none" />
            <span className="sr-only">Close</span>
          </Dialog.Close>

          {/* Back button for add mode */}
          {dialogMode === "add" && (
            <button
              onClick={handleSwitchToSelectMode}
              className="absolute top-2 left-2 rounded-full p-2 opacity-70 transition-opacity hover:opacity-100 focus:outline-none"
            >
              <ArrowLeft className="h-6 w-6 text-gray-600" />
              <span className="sr-only">Back</span>
            </button>
          )}

          {dialogMode === "select" ? (
            <>
              <Dialog.Title className="text-lg font-medium text-gray-900">
                Select Lesson Participants
              </Dialog.Title>
              <Dialog.Description className="mb-4 text-sm text-gray-500">
                Select additional participants for the lesson. Maximum {maxPlayers} participants
                allowed.
              </Dialog.Description>

              <div className="max-h-96 space-y-4 overflow-y-auto">
                {playerListLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
                  </div>
                ) : (
                  players.map((player) => (
                    <div
                      key={player.id}
                      className={`flex items-center justify-between rounded-full border p-3 transition-colors ${
                        player.isSelected
                          ? "border-primary bg-primary/5"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full">
                            <span className="text-primary text-sm font-medium">
                              {player.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">{player.name}</p>
                          {player?.email && <p className="text-xs text-gray-500">{player.email}</p>}
                        </div>
                      </div>

                      <button
                        onClick={() => onPlayerToggle(player.id)}
                        disabled={!player.isSelected && selectedPlayersCount >= maxPlayers}
                        className={`flex h-6 w-6 items-center justify-center rounded border-2 transition-colors ${
                          player.isSelected
                            ? "border-primary bg-primary text-white"
                            : "hover:border-primary border-gray-300"
                        } ${
                          !player.isSelected && selectedPlayersCount >= maxPlayers
                            ? "cursor-not-allowed opacity-50"
                            : "cursor-pointer"
                        }`}
                      >
                        {player.isSelected && (
                          <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        )}
                      </button>
                    </div>
                  ))
                )}
              </div>

              <div className="flex justify-between space-x-2 pt-4">
                <button
                  onClick={handleSwitchToAddMode}
                  className="bg-primary hover:bg-primary/90 flex items-center gap-2 rounded-full px-4 py-2 text-white transition-colors"
                >
                  <Plus className="h-4 w-4" />
                  Add New Participant
                </button>
                <button
                  onClick={handleClose}
                  className="rounded-full bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-200"
                >
                  Done
                </button>
              </div>
            </>
          ) : (
            <>
              <Dialog.Title className="ml-8 text-lg font-medium text-gray-900">
                Add New Participant
              </Dialog.Title>
              <Dialog.Description className="mb-4 ml-8 text-sm text-gray-500">
                Add a new participant to your list for future lesson bookings.
              </Dialog.Description>

              <div className="space-y-4">
                {/* Name Field */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Participant Name
                  </label>
                  <input
                    type="text"
                    className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
                    value={newPlayerData.name}
                    onChange={(e) =>
                      setNewPlayerData((prev) => ({ ...prev, name: e.target.value }))
                    }
                    placeholder="Enter participant name"
                    disabled={isAddingPlayer}
                  />
                </div>

                {/* Email Field */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Email</label>
                  <input
                    type="email"
                    className="focus:ring-primary w-full rounded-full border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:outline-none"
                    value={newPlayerData.email}
                    onChange={(e) =>
                      setNewPlayerData((prev) => ({ ...prev, email: e.target.value }))
                    }
                    placeholder="Enter email address"
                    disabled={isAddingPlayer}
                  />
                </div>

                {/* Phone Field */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Phone Number</label>
                  <div className="flex overflow-hidden rounded-full border border-gray-300">
                    <select
                      value={newPlayerData.country_code}
                      onChange={(e) =>
                        setNewPlayerData((prev) => ({ ...prev, country_code: e.target.value }))
                      }
                      className="border-r border-gray-300 bg-gray-50 px-3 py-2 text-sm focus:outline-none"
                      disabled={isAddingPlayer}
                    >
                      <option value="+1">🇺🇸 +1</option>
                    </select>
                    <input
                      type="tel"
                      placeholder="Mobile Number"
                      value={newPlayerData.phone}
                      onChange={(e) => {
                        let value = e.target.value;
                        if (e.target.value.length <= 10) {
                          value = value.replace(/\D/g, "");
                        } else {
                          value = value.slice(0, 10);
                        }
                        setNewPlayerData((prev) => ({ ...prev, phone: value }));
                      }}
                      className="flex-1 rounded-full px-3 py-2 text-black focus:outline-none"
                      disabled={isAddingPlayer}
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <button
                  onClick={handleSwitchToSelectMode}
                  className="rounded-full bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-200"
                  disabled={isAddingPlayer}
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddPlayer}
                  disabled={
                    isAddingPlayer ||
                    !newPlayerData.name ||
                    !newPlayerData.email ||
                    !newPlayerData.phone
                  }
                  className="bg-primary hover:bg-primary/90 rounded-full px-4 py-2 text-sm font-medium text-white transition-colors disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {isAddingPlayer ? <Loader /> : "Add Participant"}
                </button>
              </div>
            </>
          )}
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default LessonPlayerManagementDialog;
