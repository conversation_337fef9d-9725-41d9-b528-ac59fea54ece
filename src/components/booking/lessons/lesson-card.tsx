import React from "react";
import { Lesson } from "./booking-lesson-section";

const LessonCard = ({
  lesson,
  onBookNow,
}: {
  lesson: Lesson;
  onBookNow: (lesson: Lesson) => void;
}) => {
  return (
    <div className="group inline-flex w-[300px] flex-col items-center justify-start gap-[15px] rounded-[20px] bg-white px-5 py-5 shadow-[0px_0px_15px_0px_rgba(0,0,0,0.08)] outline outline-[#ddba0a]">
      <img
        className="h-[79px] w-full rounded-[10px] object-cover object-center duration-300 group-hover:scale-105"
        src={lesson.cover_image || "/imgs/placeholder.svg"}
      />
      <div className="flex flex-col items-center justify-start self-stretch">
        <div className="font-helvetica h-[23px] justify-start self-stretch text-center text-xl leading-[25px] font-bold text-black">
          {lesson?.name || "Unknown Lesson"}
        </div>
      </div>

      <div className="flex flex-col items-start justify-start self-stretch">
        <div className="inline-flex items-center justify-start self-stretch">
          <div className="relative size-6 overflow-hidden">
            <div data-svg-wrapper className="absolute top-[3px] left-[3px]">
              <svg
                width="18"
                height="18"
                viewBox="0 0 18 18"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M8.69922 4.3502V8.7002L11.5992 10.1502M15.9492 8.7002C15.9492 12.7043 12.7033 15.9502 8.69922 15.9502C4.69516 15.9502 1.44922 12.7043 1.44922 8.7002C1.44922 4.69613 4.69516 1.4502 8.69922 1.4502C12.7033 1.4502 15.9492 4.69613 15.9492 8.7002Z"
                  stroke="#1C5534"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>
          <div className="font-helvetica justify-start text-sm font-normal text-black">
            Minimum {lesson.min_duration} mins
          </div>
        </div>
        <div className="inline-flex items-center justify-start self-stretch">
          <div className="relative size-6 overflow-hidden">
            <div data-svg-wrapper className="absolute top-[3px] left-[3px]">
              <svg
                width="18"
                height="18"
                viewBox="0 0 18 18"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M15.9492 15.2248V13.7748C15.9492 12.4235 15.025 11.2881 13.7742 10.9662M11.2367 2.3856C12.2995 2.81581 13.0492 3.85776 13.0492 5.07481C13.0492 6.29186 12.2995 7.3338 11.2367 7.76401M12.3242 15.2248C12.3242 13.8736 12.3242 13.198 12.1035 12.665C11.8091 11.9544 11.2446 11.3899 10.534 11.0956C10.0011 10.8748 9.32545 10.8748 7.97422 10.8748H5.79922C4.44799 10.8748 3.77237 10.8748 3.23944 11.0956C2.52886 11.3899 1.9643 11.9544 1.66997 12.665C1.44922 13.198 1.44922 13.8736 1.44922 15.2248M9.78672 5.07481C9.78672 6.67643 8.48835 7.97481 6.88672 7.97481C5.28509 7.97481 3.98672 6.67643 3.98672 5.07481C3.98672 3.47318 5.28509 2.1748 6.88672 2.1748C8.48835 2.1748 9.78672 3.47318 9.78672 5.07481Z"
                  stroke="#1C5534"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>
          <div className="font-helvetica justify-center text-sm font-normal text-black">
            Min Players: {lesson.min_player} - Max Players : {lesson.max_player}
          </div>
        </div>
      </div>
      <button
        onClick={() => onBookNow(lesson)}
        className="font-helvetica inline-flex items-center justify-center gap-2.5 self-stretch rounded-[30px] bg-[#ddba0a] text-center text-base leading-[41px] font-bold tracking-tight text-[#1c5534] duration-300 hover:scale-[102%]"
      >
        Book Lesson
      </button>
    </div>
  );
};

export default LessonCard;
