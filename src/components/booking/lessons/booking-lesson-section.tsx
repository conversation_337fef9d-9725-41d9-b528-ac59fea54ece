import { useGetPublicLessonList } from "@/api/booking-lesson-service";
import { useCallback, useState, useEffect } from "react";
import LessonCard from "./lesson-card";
import LessonDetailsStep from "./lesson-details-step";
import { useBookingStore } from "@/store/booking-store";
import LessonCardSkeleton from "./lesson-card-skeleton";

export interface Lesson {
  id: number;
  name: string;
  min_duration: number;
  max_duration: number;
  default_duration: number;
  min_player: number;
  max_player: number;

  created_at: string;
  updated_at: string;
  cover_image: string;
  cancel_before_start_in_minutes: number;
  advance_minutes_in_minutes: number;
}

const BookingLessonSection = ({ locationId }: { locationId: number }) => {
  const [currentStep, setCurrentStep] = useState<"list" | "details">("list");
  const [selectedLesson, setSelectedLesson] = useState<Lesson | null>(null);

  const { clearAllBookings, lessonBookingDetails } = useBookingStore();

  const {
    publicLessonList,
    publicLessonListLoading,
    publicLessonListError,
    publicLessonListEmpty,
  } = useGetPublicLessonList({ page: 1, limit: 100 });

  // Show details step when returning from checkout with existing lessonBookingDetails
  useEffect(() => {
    if (lessonBookingDetails && lessonBookingDetails.lesson_id && publicLessonList) {
      const lesson = publicLessonList.find((l: Lesson) => l.id === lessonBookingDetails.lesson_id);
      if (lesson) {
        setSelectedLesson(lesson);
        setCurrentStep("details");
      }
    }
  }, [lessonBookingDetails, publicLessonList]);

  const handleBookNow = useCallback((lesson: Lesson) => {
    setSelectedLesson(lesson);

    setCurrentStep("details");
  }, []);

  const handleBackToList = useCallback(() => {
    setCurrentStep("list");
    setSelectedLesson(null);
    clearAllBookings();
  }, []);

  return (
    <div className="container mx-auto px-2 py-1 lg:px-4">
      <div className="inline-flex w-full flex-col items-center justify-start">
        <div className="font-helvetical justify-center self-stretch text-center text-[25px] leading-[25px] font-bold text-black">
          Type of Lessons
        </div>
        <div className="font-helvetical max-w-2xl justify-center text-center text-[15px] leading-relaxed font-normal text-[#364153]">
          Lessons are flexible in timing and are ideal for all skill levels, whether you're just
          starting out or polishing your competitive edge. With one-on-one or in group guidance,
          you&apos;ll progress faster and smarter.
        </div>
      </div>

      {currentStep !== "details" || !selectedLesson ? (
        <div className="mt-4">
          {publicLessonListLoading ? (
            <div className="grid grid-cols-1 items-center justify-items-center gap-6 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4">
              {Array.from({ length: 6 }).map((_, index) => (
                <LessonCardSkeleton key={index} />
              ))}
            </div>
          ) : publicLessonListError ? (
            <div>Error: {publicLessonListError.message}</div>
          ) : publicLessonListEmpty ? (
            <div>No programs available</div>
          ) : (
            <div className="grid grid-cols-1 items-center justify-items-center gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {publicLessonList.map((lesson: Lesson) => (
                <LessonCard key={lesson.id} lesson={lesson} onBookNow={handleBookNow} />
              ))}
            </div>
          )}
        </div>
      ) : (
        <LessonDetailsStep
          locationId={locationId}
          lesson={selectedLesson}
          onBack={handleBackToList}
        />
      )}
    </div>
  );
};

export default BookingLessonSection;
