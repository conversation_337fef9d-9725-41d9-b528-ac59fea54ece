import React from "react";
import { Instructor } from "./booking-instructor-section";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const InstructorCard = ({
  instructor,
  onBookNow,
}: {
  instructor: Instructor;
  onBookNow: (instructor: Instructor) => void;
}) => {
  // Get initials for fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Format focus areas for display
  const focusAreasText =
    instructor.focus_areas?.map((area) => area.name).join(", ") || "General instruction";

  return (
    <div className="inline-flex w-full max-w-[350px] flex-col items-start justify-start gap-4 rounded-[20px] bg-white p-5 shadow-[0px_0px_15px_0px_rgba(0,0,0,0.08)]">
      <div className="flex w-full items-center justify-start gap-4">
        <Avatar className="h-16 w-16 shadow-lg">
          <AvatarImage
            src={instructor.profile_image?.path || "/imgs/profile.png"}
            alt={instructor.name}
            className="object-cover"
          />
          <AvatarFallback className="bg-[#ddba0a] text-lg font-semibold text-[#1c5534]">
            {getInitials(instructor.name)}
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-1 flex-col items-start justify-start">
          <div className="font-helvetica text-left text-[15px] leading-[25px] font-medium text-black">
            {instructor.name}
          </div>
          <div className="font-helvetica text-left text-xs font-normal text-[#909090]">
            {instructor.court?.name || "Instructor"}
          </div>
          {instructor.email && (
            <div className="font-helvetica text-left text-xs font-normal text-[#909090]">
              {instructor.email.email}
            </div>
          )}
        </div>
      </div>

      <div className="line-clamp-1 w-full text-left font-['Helvetica'] text-[14px] font-normal text-black">
        <span className="font-medium">Focus areas:</span> {focusAreasText}
      </div>

      <button
        onClick={() => onBookNow(instructor)}
        className="inline-flex w-full items-center justify-center gap-2.5 rounded-[30px] bg-[#ddba0a] px-4 py-3 transition-colors hover:bg-[#c4a609]"
      >
        <div className="text-center font-['Helvetica'] text-[15px] font-bold tracking-tight text-[#1c5534]">
          Book Now
        </div>
      </button>
    </div>
  );
};

export default InstructorCard;
