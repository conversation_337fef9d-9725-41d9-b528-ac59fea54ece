import { useGetPublicInstructorList } from "@/api/booking-instructor-service";
import { useCallback, useState, useEffect } from "react";
import InstructorDetailsStep from "./instructor-details-step";
import InstructorCard from "./instructor-card";
import InstructorCardSkeleton from "./instructor-card-skeleton";
import { useBookingStore } from "@/store/booking-store";

export interface Instructor {
  id: number;
  name: string;
  court_id: number;
  is_instructor: boolean;
  display_order: number;
  user_type: "INSTRUCTOR" | "USER" | "ADMIN";
  is_active: boolean;
  min_duration: number;
  max_duration: number;
  default_duration: number;
  min_player: number;
  max_player: number;
  created_at: string;
  updated_at: string;
  cover_image: string;
  cancel_before_start_in_minutes: number;
  advance_minutes_in_minutes: number;
  profile_image?: {
    id: number;
    path: string;
  };
  email?: {
    id: number;
    user_id: number;
    email: string;
    is_verified: boolean;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
  };
  phone?: {
    id: number;
    user_id: number;
    country_code: string;
    phone: string;
    is_verified: boolean;
    phone_verified_at: string | null;
    created_at: string;
    updated_at: string;
  };
  court?: {
    id: number;
    name: string;
    description: string | null;
    cover_image: string | null;
    timezone: string;
  };
  focus_areas?: Array<{
    id: number;
    name: string;
    laravel_through_key: number;
  }>;
}

const BookingInstructorSection = ({ locationId }: { locationId: number }) => {
  const { instructorBookingDetails, clearAllBookings } = useBookingStore();
  const [currentStep, setCurrentStep] = useState<"list" | "details">("list");
  const [selectedInstructor, setSelectedInstructor] = useState<Instructor | null>(null);

  const {
    publicInstructorList,
    publicInstructorListLoading,
    publicInstructorListError,
    publicInstructorListEmpty,
  } = useGetPublicInstructorList({ page: 1, limit: 100 });

  console.log("publicInstructorList", publicInstructorList);
  console.log("instructorBookingDetails from store", instructorBookingDetails);

  // Check if we have existing instructor booking details and find the instructor
  useEffect(() => {
    if (instructorBookingDetails && publicInstructorList && publicInstructorList.length > 0) {
      const instructor = publicInstructorList.find(
        (inst: Instructor) => inst.id === instructorBookingDetails.instructor_id
      );

      if (instructor) {
        setSelectedInstructor(instructor);
        setCurrentStep("details");
        console.log("Found existing instructor booking, showing details step", instructor);
      }
    }
  }, [instructorBookingDetails, publicInstructorList]);

  const handleBookNow = useCallback((instructor: Instructor) => {
    setSelectedInstructor(instructor);
    setCurrentStep("details");
  }, []);

  const handleBackToList = useCallback(() => {
    setCurrentStep("list");
    setSelectedInstructor(null);
    clearAllBookings();
  }, []);

  return (
    <div className="container mx-auto py-1 lg:px-4">
      <div className="inline-flex w-full flex-col items-center justify-start">
        <div className="font-helvetical justify-center self-stretch text-center text-[25px] leading-[25px] font-bold text-black">
          Choose Instructor
        </div>
        <div className="font-helvetical max-w-2xl justify-center px-4 text-center text-[15px] leading-relaxed font-normal text-[#364153]">
          Each instructor has a detailed profile showcasing their experience, teaching approach, and
          player reviews—so you know exactly who you&apos;re learning from. Ideal for players who
          want a consistent coach or a specific training method.
        </div>
      </div>

      {currentStep !== "details" || !selectedInstructor ? (
        <div className="mt-4">
          {publicInstructorListLoading ? (
            <div className="grid grid-cols-1 items-center justify-items-center gap-6 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3">
              {Array.from({ length: 6 }).map((_, index) => (
                <InstructorCardSkeleton key={index} />
              ))}
            </div>
          ) : publicInstructorListError ? (
            <div>Error: {publicInstructorListError.message}</div>
          ) : publicInstructorListEmpty ? (
            <div>No instructors available</div>
          ) : (
            <div className="grid grid-cols-1 items-center justify-items-center gap-6 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3">
              {publicInstructorList.map((instructor: Instructor) => (
                <InstructorCard
                  key={instructor.id}
                  instructor={instructor}
                  onBookNow={handleBookNow}
                />
              ))}
            </div>
          )}
        </div>
      ) : (
        <InstructorDetailsStep
          locationId={locationId}
          instructor={selectedInstructor}
          onBack={handleBackToList}
        />
      )}
    </div>
  );
};

export default BookingInstructorSection;
