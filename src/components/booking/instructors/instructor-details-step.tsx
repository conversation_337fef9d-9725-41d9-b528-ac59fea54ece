"use client";
import { useGetPlayerList } from "@/api/booking-checkout-service";
import { useGetPublicLessonList } from "@/api/booking-lesson-service";
import {
  useGetNextInstructorOpening,
  useGetPublicCourtById,
  useGetPublicCourtList,
} from "@/api/booking-service";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { extractErrorMessage } from "@/libs/utils";
import { useBookingStore } from "@/store/booking-store";
import { ChevronDown, ChevronDownIcon, ChevronLeft, Minus, Plus, Users } from "lucide-react";
import { useRouter } from "next/navigation";
import { use<PERSON><PERSON>back, useMemo, useState, useEffect } from "react";
import { toast } from "sonner";
import Loader from "../../loader";
import { Button } from "../../ui/button";
import { Calendar } from "../../ui/calendar";
import LessonPlayerManagementDialog from "../lessons/lesson-player-management-dialog";
import { Instructor } from "./booking-instructor-section";
import { useLessonBookingEstimate } from "@/hooks/use-booking-estimate";
import { Skeleton } from "@/components/ui/skeleton";

// Types for the instructor booking form
interface InstructorBookingFormData {
  lessonType: string;
  date: string;
  time: string;
  duration: string;
  subCourtId: number;
  subCourtName: string;
  players: number;
}

// Type for lesson type object from API
interface LessonType {
  id: number;
  name?: string;
  min_player: number;
  max_player: number;
  min_duration: number;
  max_duration: number;
  default_duration: number;
  [key: string]: any;
}

interface SelectedDetails {
  lessonTypeId: number;
  date: string;
  duration: number;
  subCourtId: number;
}

interface InstructorDetailsStepProps {
  instructor: Instructor;
  onBack: () => void;
  locationId: number;
}

// Player interface for the dialog
interface Player {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  country_code?: string;
  isSelected: boolean;
  isCurrentUser?: boolean;
}

const InstructorDetailsStep = ({ instructor, onBack, locationId }: InstructorDetailsStepProps) => {
  const router = useRouter();
  const [isBooking, setIsBooking] = useState(false);
  const { setInstructorBookingDetails, addInstructorParticipants, instructorBookingDetails } =
    useBookingStore();

  // Player management state
  const [isPlayerDialogOpen, setIsPlayerDialogOpen] = useState(false);
  const [players, setPlayers] = useState<Player[]>([]);
  const [selectedParticipants, setSelectedParticipants] = useState<number[]>([]);

  // State management for instructor booking form - pre-populate from existing booking details
  const [bookingData, setBookingData] = useState<InstructorBookingFormData>({
    lessonType: instructorBookingDetails?.lesson_type_name || "",
    date: instructorBookingDetails?.date || "",
    time: instructorBookingDetails?.time || "",
    duration: instructorBookingDetails?.duration?.toString() || "",
    subCourtId: instructorBookingDetails?.sub_court_id || 0,
    subCourtName: instructorBookingDetails?.sub_court_name || "",
    players: instructorBookingDetails?.players || instructor.min_player || 0,
  });

  const [selectedDetails, setSelectedDetails] = useState<SelectedDetails>({
    lessonTypeId: instructorBookingDetails?.lesson_type_id || 0,
    date: instructorBookingDetails?.date || "",
    duration: instructorBookingDetails?.duration || instructor.min_duration || 60,
    subCourtId: instructorBookingDetails?.sub_court_id || 0,
  });

  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    instructorBookingDetails?.date
      ? new Date(`${instructorBookingDetails.date}T00:00:00`)
      : undefined
  );
  const [open, setOpen] = useState(false);

  // Update local state when instructorBookingDetails changes (similar to court booking)
  useEffect(() => {
    if (instructorBookingDetails) {
      setBookingData({
        lessonType: instructorBookingDetails.lesson_type_name || "",
        date: instructorBookingDetails.date || "",
        time: instructorBookingDetails.time || "",
        duration: instructorBookingDetails.duration?.toString() || "",
        subCourtId: instructorBookingDetails.sub_court_id || 0,
        subCourtName: instructorBookingDetails.sub_court_name || "",
        players: instructorBookingDetails.players || instructor.min_player || 0,
      });

      setSelectedDetails({
        lessonTypeId: instructorBookingDetails.lesson_type_id || 0,
        date: instructorBookingDetails.date || "",
        duration: instructorBookingDetails.duration || instructor.min_duration || 60,
        subCourtId: instructorBookingDetails.sub_court_id || 0,
      });

      if (instructorBookingDetails.date) {
        setSelectedDate(new Date(`${instructorBookingDetails.date}T00:00:00`));
      }

      // Pre-populate selected participants if they exist
      if (
        instructorBookingDetails.participants &&
        instructorBookingDetails.participants.length > 0
      ) {
        setSelectedParticipants(instructorBookingDetails.participants);
      }
    }
  }, [instructorBookingDetails, instructor.min_player, instructor.min_duration]);

  const { publicCourtList, publicCourtListLoading } = useGetPublicCourtList({
    page: 1,
    limit: 10,
    courtLocationId: locationId,
  });

  const firstCourtId = useMemo(() => {
    return publicCourtList && publicCourtList.length > 0 ? publicCourtList[0].id : null;
  }, [publicCourtList]);

  const { publicCourtDetails } = useGetPublicCourtById({
    courtId: firstCourtId,
  });

  // Get lesson types list (instead of instructor list)
  const { publicLessonList, publicLessonListLoading, publicLessonListError } =
    useGetPublicLessonList({ page: 1, limit: 100 });

  // Get player list for dialog
  const { playerList, playerListLoading, revalidateGetPlayerList } = useGetPlayerList();

  const { nextInstructorOpening, nextInstructorOpeningLoading } = useGetNextInstructorOpening({
    lessonTypeId: selectedDetails?.lessonTypeId,
    date: selectedDetails?.date,
    instructorId: instructor.id,
    subCourtId: selectedDetails?.subCourtId,
    duration: selectedDetails?.duration,
  });

  // Available sub-courts
  const availableSubCourts = useMemo(() => {
    if (!publicCourtDetails?.sub_courts) return [];
    return publicCourtDetails?.sub_courts;
  }, [publicCourtDetails]);

  // console.log("publicCourtDetails", publicCourtDetails);
  // console.log("sub court list", availableSubCourts);

  // Duration options based on selected lesson type
  const durationOptions = useMemo(() => {
    const selectedLessonType = publicLessonList.find(
      (lessonType: LessonType) => lessonType.id === selectedDetails.lessonTypeId
    );
    if (!selectedLessonType) return [];

    const options = [];
    for (
      let duration = selectedLessonType.min_duration;
      duration <= selectedLessonType.max_duration;
      duration += 30
    ) {
      options.push(duration);
    }
    return options;
  }, [publicLessonList, selectedDetails.lessonTypeId]);

  // Player count options based on selected lesson type
  const playerCountOptions = useMemo(() => {
    const selectedLessonType = publicLessonList.find(
      (lessonType: LessonType) => lessonType.id === selectedDetails.lessonTypeId
    );
    if (!selectedLessonType) return [];

    // For private lessons (max_player <= 1), don't show player selection
    if (selectedLessonType.max_player <= 1) return [];

    const options = [];
    for (
      let count = selectedLessonType.min_player;
      count <= selectedLessonType.max_player;
      count++
    ) {
      options.push(count);
    }
    return options;
  }, [publicLessonList, selectedDetails.lessonTypeId]);

  // Player management functions
  const loadPlayers = useCallback(() => {
    if (playerListLoading || !playerList) return;

    const formattedPlayers: Player[] = playerList.map((player: any) => ({
      id: player.id.toString(),
      name: player.name || `Player ${player.id}`,
      email: player.email,
      phone: player.phone,
      country_code: player.country_code,
      isSelected: selectedParticipants.includes(player.id),
      isCurrentUser: false, // For lessons, we don't need to mark current user
    }));

    setPlayers(formattedPlayers);
  }, [playerList, playerListLoading, selectedParticipants]);

  // Auto-reload players when playerList data changes (e.g., after revalidation)
  useEffect(() => {
    if (playerList && !playerListLoading) {
      loadPlayers();
    }
  }, [playerList, playerListLoading, loadPlayers]);

  const handleOpenPlayerDialog = useCallback(() => {
    setIsPlayerDialogOpen(true);
    loadPlayers();
  }, [loadPlayers]);

  const handleClosePlayerDialog = useCallback(() => {
    setIsPlayerDialogOpen(false);

    // Get selected player IDs
    const selectedPlayerIds = players
      .filter((player) => player.isSelected)
      .map((player) => parseInt(player.id))
      .filter((id) => !isNaN(id));

    setSelectedParticipants(selectedPlayerIds);
    addInstructorParticipants(selectedPlayerIds);

    console.log("Selected participants saved to booking store:", selectedPlayerIds);
  }, [players, addInstructorParticipants]);

  // Handle lesson type selection
  const handleLessonTypeSelect = useCallback(
    (lessonType: LessonType) => {
      setSelectedDetails((prev) => ({
        ...prev,
        lessonTypeId: lessonType.id,
        duration: lessonType.min_duration,
      }));
      setBookingData((prev) => ({
        ...prev,
        lessonType: lessonType.name || "",
        duration: lessonType.min_duration.toString(),
        players: lessonType.min_player,
      }));

      // Update instructor booking details in store
      if (instructorBookingDetails) {
        setInstructorBookingDetails({
          ...instructorBookingDetails,
          lesson_type_id: lessonType.id,
          lesson_type_name: lessonType.name || "",
          duration: lessonType.min_duration,
          players: lessonType.min_player,
        });
      }
    },
    [instructorBookingDetails, setInstructorBookingDetails]
  );

  // Handle date selection
  const handleDateSelect = useCallback(
    (date: Date | undefined) => {
      if (date) {
        // console.log("instructor booking selected date", date);
        setSelectedDate(date);
        const year = date!.getFullYear();
        const month = String(date!.getMonth() + 1).padStart(2, "0");
        const day = String(date!.getDate()).padStart(2, "0");
        const formattedDate = `${year}-${month}-${day}`;
        setSelectedDetails((prev) => ({
          ...prev,
          date: formattedDate,
        }));
        setBookingData((prev) => ({
          ...prev,
          date: formattedDate,
        }));

        // Update instructor booking details in store
        if (instructorBookingDetails) {
          setInstructorBookingDetails({
            ...instructorBookingDetails,
            date: formattedDate,
          });
        }
      }
    },
    [instructorBookingDetails, setInstructorBookingDetails]
  );

  // Handle time selection
  const handleTimeSelect = useCallback(
    (time: string) => {
      setBookingData((prev) => ({
        ...prev,
        time: time,
      }));

      // Update instructor booking details in store
      if (instructorBookingDetails) {
        const startTime = `${instructorBookingDetails.date || bookingData.date} ${time}:00`;
        setInstructorBookingDetails({
          ...instructorBookingDetails,
          time: time,
          start_time: startTime,
        });
      }
    },
    [instructorBookingDetails, setInstructorBookingDetails, bookingData.date]
  );

  // Handle duration selection
  const handleDurationSelect = useCallback(
    (duration: number) => {
      setSelectedDetails((prev) => ({
        ...prev,
        duration: duration,
      }));
      setBookingData((prev) => ({
        ...prev,
        duration: duration.toString(),
      }));

      // Update instructor booking details in store
      if (instructorBookingDetails) {
        setInstructorBookingDetails({
          ...instructorBookingDetails,
          duration: duration,
        });
      }
    },
    [instructorBookingDetails, setInstructorBookingDetails]
  );

  // Handle sub-court selection
  const handleSubCourtSelect = useCallback(
    (subCourt: any) => {
      setSelectedDetails((prev) => ({
        ...prev,
        subCourtId: subCourt.id,
      }));
      setBookingData((prev) => ({
        ...prev,
        subCourtId: subCourt.id,
        subCourtName: subCourt.name,
      }));

      // Update instructor booking details in store
      if (instructorBookingDetails) {
        setInstructorBookingDetails({
          ...instructorBookingDetails,
          sub_court_id: subCourt.id,
          sub_court_name: subCourt.name,
        });
      }
    },
    [instructorBookingDetails, setInstructorBookingDetails]
  );

  // Handle player count change
  const handlePlayerCountChange = useCallback(
    (count: number) => {
      setBookingData((prev) => ({
        ...prev,
        players: count,
      }));

      // Update instructor booking details in store
      if (instructorBookingDetails) {
        setInstructorBookingDetails({
          ...instructorBookingDetails,
          players: count,
        });
      }
    },
    [instructorBookingDetails, setInstructorBookingDetails]
  );

  const handlePlayerToggle = useCallback((playerId: string) => {
    setPlayers((prevPlayers) =>
      prevPlayers.map((player) =>
        player.id === playerId ? { ...player, isSelected: !player.isSelected } : player
      )
    );
    setSelectedParticipants((prevParticipants) =>
      prevParticipants.includes(parseInt(playerId))
        ? prevParticipants.filter((id) => id !== parseInt(playerId))
        : [...prevParticipants, parseInt(playerId)]
    );
  }, []);

  const handlePlayerAdded = useCallback(() => {
    revalidateGetPlayerList();
  }, [revalidateGetPlayerList]);

  // Handler for booking now
  const handleBookNow = useCallback(async () => {
    try {
      setIsBooking(true);

      // Validate required fields
      if (
        !bookingData.lessonType ||
        !bookingData.date ||
        !bookingData.time ||
        !bookingData.duration
      ) {
        toast.error("Please fill in all required fields");
        return;
      }

      // Find selected lesson type
      const selectedLessonType = publicLessonList.find(
        (lessonType: LessonType) => lessonType.id === selectedDetails.lessonTypeId
      );

      if (!selectedLessonType) {
        toast.error("Please select a lesson type");
        return;
      }

      // Create instructor booking details
      const instructorBookingDetails = {
        instructor_id: instructor.id,
        lesson_type_id: selectedDetails.lessonTypeId,
        type: "Beginner",
        start_time: `${bookingData.date} ${bookingData.time}:00`,
        duration: selectedDetails.duration,
        sub_court_id: selectedDetails.subCourtId,
        // Additional display data for checkout
        date: bookingData.date,
        time: bookingData.time,
        players: bookingData.players,
        lesson_type_name: selectedLessonType.name || "Lesson",
        instructor_name: instructor.name || "Instructor",
        sub_court_name: bookingData.subCourtName,
        split: false,
      };

      console.log("Saving instructor booking details to store:", instructorBookingDetails);
      setInstructorBookingDetails(instructorBookingDetails);

      // Add participants if any are selected
      if (selectedParticipants.length > 0) {
        addInstructorParticipants(selectedParticipants);
      }

      toast.success("Instructor booking details saved! Redirecting to checkout...");
      router.push("/checkout?booking_type=instructor");
    } catch (error: any) {
      console.error("Error saving instructor booking details:", error);
      toast.error(extractErrorMessage(error) || "Failed to save instructor booking details");
    } finally {
      setIsBooking(false);
    }
  }, [
    bookingData,
    selectedDetails,
    instructor,
    publicLessonList,
    router,
    setInstructorBookingDetails,
  ]);

  // Prepare estimate request data
  const estimateRequestData = useMemo(() => {
    if (!instructorBookingDetails) return null;

    return {
      lesson_id: selectedDetails.lessonTypeId,
      instructor_id: instructor.id,
      lesson_type_id: selectedDetails.lessonTypeId,
      type: "Beginner",
      start_time: `${bookingData.date} ${bookingData.time}:00`,
      duration: selectedDetails.duration,
      sub_court_id: selectedDetails.subCourtId,
      split: false,
      participants: instructorBookingDetails.participants || [],
    };
  }, [instructorBookingDetails, selectedDetails, bookingData, instructor]);

  const { estimateData, isLoading, error } = useLessonBookingEstimate(estimateRequestData);

  return (
    <div className="container mx-auto p-4">
      <div className="flex w-full flex-col items-start justify-start gap-4 lg:gap-10">
        <div className="relative flex w-full flex-col items-start justify-start gap-4 rounded-[30px] bg-white p-4 lg:flex-row lg:items-start lg:justify-between lg:gap-10 lg:p-10">
          {/* Left Section - Form Controls */}
          <div className="flex w-full flex-col items-start justify-start gap-3 px-4 lg:w-1/2">
            {/* Back Button */}
            <button
              onClick={onBack}
              className="absolute top-4 left-1 z-10 flex items-center gap-2 text-[#1c5534] transition-colors hover:text-[#0f3a1f]"
            >
              <ChevronLeft className="h-8 w-8" />
            </button>

            {/* Instructor Info */}
            <div className="flex w-full items-center gap-4">
              <Avatar className="h-12 w-12 shadow-lg">
                <AvatarImage
                  src={instructor.profile_image?.path || "/imgs/profile.png"}
                  alt={instructor.name}
                  className="object-cover"
                />
                <AvatarFallback className="bg-[#ddba0a] text-sm font-semibold text-[#1c5534]">
                  {instructor.name
                    ?.split(" ")
                    .map((n) => n[0])
                    .join("")
                    .toUpperCase()
                    .slice(0, 2)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h3 className="font-helvetica text-base font-semibold text-black">
                  {instructor.name}
                </h3>
                <p className="font-helvetica text-sm text-gray-600">{instructor.court?.name}</p>
                {instructor.focus_areas && instructor.focus_areas.length > 0 && (
                  <p className="font-helvetica text-xs text-gray-500">
                    Focus: {instructor.focus_areas.map((area) => area.name).join(", ")}
                  </p>
                )}
              </div>
            </div>

            {/* Lesson Type Selection */}
            <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
              Select Lesson Type
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-[25px] py-2 text-[15px] leading-[35px] font-normal text-black transition-colors hover:bg-gray-50">
                <span className="font-helvetica">
                  {bookingData.lessonType || "Select lesson type"}
                </span>
                <ChevronDown className="h-4 w-4" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] min-w-[var(--radix-dropdown-menu-trigger-width)] rounded-[20px] border border-[#c3c3c3] bg-white shadow-lg">
                {publicLessonListLoading ? (
                  <DropdownMenuItem className="cursor-default px-[25px] py-3">
                    <span className="font-helvetica text-[15px] font-normal text-[#c3c3c3]">
                      Loading lesson types...
                    </span>
                  </DropdownMenuItem>
                ) : publicLessonListError ? (
                  <DropdownMenuItem className="cursor-default px-[25px] py-3">
                    <span className="font-helvetica text-[15px] font-normal text-red-500">
                      Error loading lesson types
                    </span>
                  </DropdownMenuItem>
                ) : (
                  publicLessonList.map((lessonType: LessonType) => (
                    <DropdownMenuItem
                      key={lessonType.id}
                      onClick={() => handleLessonTypeSelect(lessonType)}
                      className="font-helvetica cursor-pointer px-[25px] py-3 text-[15px] font-normal text-black transition-colors hover:bg-gray-50 focus:bg-gray-50"
                    >
                      {lessonType.name}
                    </DropdownMenuItem>
                  ))
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Sub-court Selection */}
            <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
              Select Court
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-6 py-2 transition-colors hover:bg-gray-50">
                <span className="font-helvetica text-[15px] leading-[35px] font-normal text-black">
                  {bookingData.subCourtName || "Select court"}
                </span>
                <ChevronDown className="h-4 w-4" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] min-w-[var(--radix-dropdown-menu-trigger-width)] rounded-[20px] border border-[#c3c3c3] bg-white shadow-lg">
                {publicCourtListLoading ? (
                  <DropdownMenuItem className="cursor-default px-[25px] py-3">
                    <span className="font-helvetica text-[15px] font-normal text-[#c3c3c3]">
                      Loading courts...
                    </span>
                  </DropdownMenuItem>
                ) : availableSubCourts.length === 0 ? (
                  <DropdownMenuItem className="cursor-default px-[25px] py-3">
                    <span className="font-helvetica text-[15px] font-normal text-[#c3c3c3]">
                      No courts available
                    </span>
                  </DropdownMenuItem>
                ) : (
                  availableSubCourts.map((subCourt: any) => (
                    <DropdownMenuItem
                      key={subCourt.id}
                      onClick={() => handleSubCourtSelect(subCourt)}
                      className="font-helvetica cursor-pointer px-[25px] py-3 text-[15px] font-normal text-black transition-colors hover:bg-gray-50 focus:bg-gray-50"
                    >
                      {subCourt.name}
                    </DropdownMenuItem>
                  ))
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Date Selection */}
            <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
              Select date
            </div>
            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  id="date"
                  className="font-helvetica flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-[25px] py-6 text-[15px] leading-[35px] font-normal text-black transition-colors hover:bg-gray-50"
                >
                  {selectedDate
                    ? `${selectedDate.getFullYear()}-${String(selectedDate.getMonth() + 1).padStart(2, "0")}-${String(selectedDate.getDate()).padStart(2, "0")}`
                    : "Select date"}
                  <ChevronDownIcon />
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto overflow-hidden border-gray-200 p-0 shadow-lg"
                align="start"
              >
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={handleDateSelect}
                  disabled={(date) => date < new Date()}
                />
              </PopoverContent>
            </Popover>

            {/* Time Selection */}
            <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
              Select Time
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-[25px] py-2 transition-colors hover:bg-gray-50">
                <span className="font-helvetica text-[15px] leading-[35px] font-normal text-black">
                  {bookingData.time || "Select time"}
                </span>
                <ChevronDown className="h-4 w-4" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] max-w-[90vw] rounded-[20px] border border-[#c3c3c3] bg-white p-4 shadow-lg">
                {nextInstructorOpeningLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <span className="font-helvetica text-[15px] font-normal text-[#c3c3c3]">
                      Loading times...
                    </span>
                  </div>
                ) : nextInstructorOpening &&
                  nextInstructorOpening?.all_slots &&
                  nextInstructorOpening?.all_slots?.length > 0 ? (
                  <div className="space-y-3">
                    <div className="font-helvetica flex items-center justify-between text-sm text-gray-600">
                      <span>Available times</span>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <div className="h-3 w-3 rounded-full bg-[#ddba0a]"></div>
                          <span className="text-xs">Peak</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <div className="h-3 w-3 rounded-full bg-gray-300"></div>
                          <span className="text-xs">Unavailable</span>
                        </div>
                      </div>
                    </div>
                    <div className="scrollbar-hide grid max-h-[300px] grid-cols-3 gap-2 overflow-y-auto lg:grid-cols-4 xl:grid-cols-6">
                      {nextInstructorOpening.all_slots.map((slot: any, index: number) => {
                        const isAvailable = nextInstructorOpening?.possible_slots?.some(
                          (availableSlot: any) => availableSlot.start === slot.start
                        );

                        return (
                          <button
                            key={index}
                            onClick={() => isAvailable && handleTimeSelect(slot?.start)}
                            disabled={!isAvailable}
                            className={`font-helvetica relative rounded-[12px] px-3 py-2 text-sm font-normal transition-all duration-200 ${
                              isAvailable
                                ? `cursor-pointer border border-[#c3c3c3] ${bookingData.time === slot?.start ? "border-[#1c5534] bg-[#1c5534] text-white" : "bg-white text-black hover:border-[#1c5534] hover:bg-gray-50"} `
                                : "cursor-not-allowed border border-gray-200 bg-gray-100 text-gray-400"
                            }`}
                          >
                            <div className="flex flex-col items-center">
                              <span className="text-[13px] leading-tight">{slot?.start}</span>
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center py-8">
                    <span className="font-helvetica text-[15px] font-normal text-[#c3c3c3]">
                      No time slots available
                    </span>
                  </div>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Right Section - Duration and Player Controls */}
          <div className="flex w-full flex-col items-center justify-center px-4 lg:w-1/2">
            {/* Duration Selection */}
            <div className="font-helvetica w-full pb-2 text-base leading-[35px] font-normal text-black">
              Select duration
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-6 py-2 transition-colors hover:bg-gray-50">
                <span className="font-helvetica text-[15px] leading-[35px] font-normal text-black">
                  {bookingData.duration ? `${bookingData.duration} minutes` : "Choose duration"}
                </span>
                <ChevronDown className="h-4 w-4 text-gray-500" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] rounded-[20px] border border-[#c3c3c3] bg-white p-2 shadow-lg">
                {durationOptions.map((duration) => (
                  <DropdownMenuItem
                    key={duration}
                    onClick={() => handleDurationSelect(duration)}
                    className="font-helvetica cursor-pointer rounded-[15px] px-4 py-2 text-[15px] font-normal text-black transition-colors hover:bg-gray-50 focus:bg-gray-50"
                  >
                    {duration} minutes
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Player Count */}
            {playerCountOptions.length > 0 && (
              <div className="flex w-full flex-col gap-4 pt-6">
                <div className="flex w-full items-center justify-between">
                  <div className="font-helvetica text-base leading-[35px] font-normal text-black">
                    Number of Players
                  </div>
                  <div className="flex items-center justify-center gap-4">
                    <button
                      onClick={() =>
                        handlePlayerCountChange(
                          Math.max(playerCountOptions[0], bookingData.players - 1)
                        )
                      }
                      disabled={bookingData.players <= playerCountOptions[0]}
                      className="flex h-[42px] w-[42px] items-center justify-center rounded-full border border-[#D8D8D8] bg-white transition-colors hover:bg-gray-50"
                    >
                      <Minus className="h-4 w-4 text-black" />
                    </button>
                    <div className="font-helvetica min-w-[60px] text-center text-[15px] leading-[35px] font-normal text-black">
                      {bookingData.players}
                    </div>
                    <button
                      onClick={() =>
                        handlePlayerCountChange(
                          Math.min(
                            playerCountOptions[playerCountOptions.length - 1],
                            bookingData.players + 1
                          )
                        )
                      }
                      disabled={
                        bookingData.players >= playerCountOptions[playerCountOptions.length - 1]
                      }
                      className="flex h-[42px] w-[42px] items-center justify-center rounded-full border border-[#D8D8D8] bg-white transition-colors hover:bg-gray-50"
                    >
                      <Plus className="h-4 w-4 text-black" />
                    </button>
                  </div>
                </div>

                {/* Add Player Button */}
                <div className="flex w-full items-center justify-between">
                  <div className="font-helvetica text-sm text-gray-600">
                    {selectedParticipants.length > 0
                      ? `${selectedParticipants.length} participant${selectedParticipants.length > 1 ? "s" : ""} selected`
                      : "No participants selected"}
                  </div>
                  <button
                    onClick={handleOpenPlayerDialog}
                    className="hover:bg-primary flex items-center gap-2 rounded-full px-4 py-2 text-sm font-medium text-[#1c5534] transition-colors hover:text-white"
                  >
                    <Users className="h-4 w-4" />
                    Add Players
                  </button>
                </div>
              </div>
            )}

            {/* Booking Details Section */}
            <div className="mt-4 flex w-full flex-col items-start justify-start gap-10 py-2 lg:mt-10">
              <div className="flex w-full flex-col items-start justify-between gap-4 rounded-[20px] border-[#ddba0a] bg-[#fffaed] p-4 shadow-[0px_-4px_20px_0px_rgba(0,0,0,0.06)] sm:flex-row sm:items-end sm:p-6">
                <div className="flex-1">
                  <p className="font-helvetica text-base leading-snug font-medium text-black">
                    Instructor Booking Details
                  </p>
                  {!bookingData.lessonType &&
                    !bookingData.date &&
                    !bookingData.time &&
                    !bookingData.duration && (
                      <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                        Please select lesson type, date, time and duration
                      </p>
                    )}
                  {bookingData.lessonType && (
                    <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                      Lesson Type: {bookingData.lessonType}
                    </p>
                  )}
                  {bookingData.date && (
                    <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                      Date: {bookingData.date}
                    </p>
                  )}
                  {bookingData.time && (
                    <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                      Time: {bookingData.time}
                    </p>
                  )}
                  {bookingData.duration && (
                    <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                      Duration: {bookingData.duration}
                    </p>
                  )}
                  {bookingData.players > 1 && (
                    <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                      Players: {bookingData.players}
                    </p>
                  )}
                </div>
                <div className="flex w-full max-w-[200px] flex-col items-center justify-center gap-1 py-2.5 sm:w-auto">
                  {isLoading ? (
                    <Skeleton className="h-10 w-20 rounded-full" />
                  ) : error ? (
                    <div className="font-helvetica text-center text-sm leading-normal text-red-500 capitalize">
                      {error}
                    </div>
                  ) : (
                    estimateData && (
                      <div className="font-helvetica max-w-[200px] text-center text-[22px] leading-normal font-bold text-[#1c5534] lg:max-w-sm">
                        Pay ${estimateData?.total.toFixed(2)}
                      </div>
                    )
                  )}
                </div>
              </div>
              <button
                onClick={handleBookNow}
                disabled={
                  isBooking ||
                  !bookingData.lessonType ||
                  !bookingData.date ||
                  !bookingData.time ||
                  !bookingData.duration ||
                  !(bookingData.players <= 1 || selectedParticipants.length === bookingData.players)
                }
                className="font-helvetica flex w-full items-center justify-center gap-2.5 rounded-[30px] bg-[#ddba0a] px-4 py-3 text-[15px] font-bold tracking-tight text-[#1c5534] transition-colors hover:bg-[#c4a609] disabled:cursor-not-allowed disabled:opacity-50"
              >
                {isBooking ? <Loader /> : "Book Now"}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Player Management Dialog */}
      <LessonPlayerManagementDialog
        isOpen={isPlayerDialogOpen}
        onClose={handleClosePlayerDialog}
        players={players}
        onPlayerToggle={handlePlayerToggle}
        selectedPlayersCount={selectedParticipants.length}
        maxPlayers={bookingData.players}
        playerListLoading={playerListLoading}
        revalidateGetPlayerList={revalidateGetPlayerList}
        onPlayerAdded={handlePlayerAdded}
      />
    </div>
  );
};

export default InstructorDetailsStep;
