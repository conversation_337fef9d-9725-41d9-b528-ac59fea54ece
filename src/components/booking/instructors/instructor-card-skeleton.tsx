import React from "react";
import { Skeleton } from "@/components/ui/skeleton";

const InstructorCardSkeleton = () => {
  return (
    <div className="inline-flex w-full max-w-[350px] flex-col items-start justify-start gap-4 rounded-[20px] bg-white p-5 shadow-[0px_0px_15px_0px_rgba(0,0,0,0.08)]">
      {/* Header with avatar and instructor info */}
      <div className="flex w-full items-center justify-start gap-4">
        {/* Avatar skeleton */}
        <Skeleton className="h-16 w-16 rounded-full shadow-lg" />
        
        {/* Instructor info skeleton */}
        <div className="flex flex-1 flex-col items-start justify-start gap-1">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-40" />
        </div>
      </div>

      {/* Focus areas skeleton */}
      <div className="w-full space-y-1">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
      </div>

      {/* Button skeleton */}
      <Skeleton className="h-12 w-full rounded-[30px]" />
    </div>
  );
};

export default InstructorCardSkeleton;
