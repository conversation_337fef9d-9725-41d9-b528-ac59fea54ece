"use client";
import { Skeleton } from "@/components/ui/skeleton";

const ProgramDetailsStepSkeleton = () => {
  return (
    <div className="container py-2">
      <div className="flex w-full flex-col items-start justify-start gap-4 rounded-[30px] bg-white p-4 lg:flex-row lg:items-start lg:justify-between lg:gap-10 lg:p-10">
        <div className="flex w-full flex-col items-start justify-start gap-4 lg:w-1/2">
          <div className="flex flex-col items-start justify-start self-stretch overflow-hidden rounded-xl bg-white pb-2.5">
            <Skeleton className="h-[200px] w-full" />

            <div className="flex flex-col items-start justify-start gap-2.5 self-stretch px-5 py-2.5">
              <div className="inline-flex items-center justify-start gap-1.5 rounded-2xl bg-[#fffaed] pr-3 pl-2.5 outline outline-offset-[-1px] outline-[#ddba0a]">
                <div className="h-2 w-2 rounded-full bg-[#ddba0a]" />
                <Skeleton className="h-4 w-16" />
              </div>

              <Skeleton className="h-7 w-3/4" />

              <div className="w-full space-y-1">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-5/6" />
                <Skeleton className="h-4 w-2/3" />
              </div>

              <div className="flex w-full flex-col items-start justify-start gap-2.5">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-32" />
                </div>

                <div className="flex items-center gap-2">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-28" />
                </div>

                <div className="flex items-center gap-2">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-24" />
                </div>
                <div className="flex items-center gap-2">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-20" />
                </div>
              </div>

              <div className="flex w-full items-center justify-between">
                <Skeleton className="h-6 w-16" />
                <div className="inline-flex items-center justify-center gap-2.5 rounded-[10px] px-[5px] py-0.5 outline outline-offset-[-1px] outline-[#c3c3c3]">
                  <Skeleton className="h-8 w-20" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="hidden h-full w-[2px] rounded-full bg-[#EBEBEB] lg:block"
          data-svg-wrapper
        ></div>

        <div className="flex w-full flex-col items-start justify-start gap-3 lg:w-1/2">
          <div className="w-full">
            <Skeleton className="mb-2 h-5 w-32" />
            <Skeleton className="h-12 w-full rounded-[20px]" />
          </div>

          <div className="w-full">
            <Skeleton className="mb-2 h-5 w-24" />
            <Skeleton className="h-12 w-full rounded-[20px]" />
          </div>

          <div className="mt-6 flex flex-col items-start justify-start gap-5 self-stretch">
            <div className="inline-flex items-end justify-between self-stretch rounded-[20px] bg-[#fffaed] px-6 pt-2.5 pb-[30px] shadow-[0px_-4px_20px_0px_rgba(0,0,0,0.06)]">
              <div className="w-full justify-start space-y-2">
                <Skeleton className="h-6 w-32" />

                <Skeleton className="h-5 w-48" />

                <Skeleton className="h-5 w-36" />

                <Skeleton className="h-5 w-40" />

                <Skeleton className="h-5 w-28" />

                <div className="mt-4 space-y-2">
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-4 w-12" />
                  </div>
                  <div className="border-t border-gray-200 pt-2">
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-5 w-20" />
                      <Skeleton className="h-6 w-20" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <Skeleton className="h-14 w-full rounded-[30px]" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgramDetailsStepSkeleton;
