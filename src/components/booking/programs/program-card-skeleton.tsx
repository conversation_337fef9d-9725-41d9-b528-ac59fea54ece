const ProgramCardSkeleton = () => {
  return (
    <div className="inline-flex w-[361px] flex-col items-start justify-start overflow-hidden rounded-xl bg-white pb-2.5 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.06)]">
      {/* Image skeleton */}
      <div className="h-[125px] w-full animate-pulse bg-gray-200" />

      <div className="flex flex-col items-start justify-start gap-2.5 self-stretch px-5 py-2">
        {/* Category pill skeleton */}
        <div className="inline-flex animate-pulse items-center justify-start gap-1.5 rounded-2xl bg-gray-100 pr-3 pl-2.5">
          <div className="h-2 w-2 rounded-full bg-gray-300" />
          <div className="h-4 w-16 rounded bg-gray-300" />
        </div>

        <div className="flex w-full flex-col items-start justify-start gap-[10px]">
          {/* Title skeleton */}
          <div className="h-6 w-3/4 animate-pulse rounded bg-gray-300" />

          <div className="flex w-full flex-col items-start justify-start gap-2">
            {/* Date range skeleton */}
            <div className="inline-flex items-center justify-start gap-2">
              <div className="h-6 w-6 animate-pulse rounded bg-gray-200" />
              <div className="h-4 w-32 animate-pulse rounded bg-gray-300" />
            </div>

            {/* Class dates skeleton */}
            <div className="flex w-full items-center justify-start gap-1">
              <div className="h-6 w-6 animate-pulse rounded bg-gray-200" />
              <div className="h-4 w-40 animate-pulse rounded bg-gray-300" />
            </div>

            {/* Time skeleton */}
            <div className="inline-flex items-center justify-start gap-2">
              <div className="h-6 w-6 animate-pulse rounded bg-gray-200" />
              <div className="h-4 w-24 animate-pulse rounded bg-gray-300" />
            </div>

            {/* Enrollment skeleton */}
            <div className="inline-flex items-center justify-start gap-2">
              <div className="h-6 w-6 animate-pulse rounded bg-gray-200" />
              <div className="h-4 w-36 animate-pulse rounded bg-gray-300" />
            </div>
          </div>

          {/* Price skeleton */}
          <div className="inline-flex w-20 items-center justify-center gap-2.5 rounded-[10px] px-[5px] py-0.5 outline outline-offset-[-1px] outline-[#c3c3c3]">
            <div className="h-7 w-12 animate-pulse rounded bg-gray-300" />
          </div>
        </div>

        {/* Button skeleton */}
        <div className="h-10 w-full animate-pulse rounded-full bg-gray-200" />
      </div>
    </div>
  );
};

export default ProgramCardSkeleton;
