import { useGetPublicProgramDetailsById } from "@/api/booking-program-service";
import Loader from "@/components/loader";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { formatDateList, formatProgramDateRange, formatProgramTimeRange } from "@/libs/utils";
import { useBookingStore } from "@/store/booking-store";
import { useProgramBookingEstimate } from "@/hooks/use-booking-estimate";
import { ChevronDown, ChevronLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useMemo, useState, useEffect } from "react";
import ProgramDetailsStepSkeleton from "./program-details-step-skeleton";

interface ProgramDetailsStepProps {
  programId: number;
  onBack: () => void;
}

// Session types for the program
const sessionTypes = [
  { id: "FULL", name: "Full Session", description: "Complete program experience" },
  { id: "PER_SESSION", name: "Single Session", description: "One-time session" },
];

interface BookingFormData {
  sessionType: string;
  selectedClassIds: number[];
}

const ProgramDetailsStep = ({ programId, onBack }: ProgramDetailsStepProps) => {
  const router = useRouter();
  const [isBooking, setIsBooking] = useState(false);
  const [classPopoverOpen, setClassPopoverOpen] = useState(false);
  const { setProgramBookingDetails, clearProgramBooking, programBookingDetails } =
    useBookingStore();
  const [selectedClasses, setSelectedClasses] = useState<
    Array<{
      id: number;
      date: string;
      start_time: string;
      end_time: string;
    }>
  >([]);
  const [bookingData, setBookingData] = useState<BookingFormData>({
    sessionType: "",
    selectedClassIds: [],
  });

  const { publicProgramDetails, publicProgramDetailsLoading } = useGetPublicProgramDetailsById({
    programId,
  });

  console.log("publicProgramDetails", publicProgramDetails);

  // Pre-populate form with existing booking details when returning from checkout
  useEffect(() => {
    if (programBookingDetails && programBookingDetails.programId === programId) {
      setBookingData({
        sessionType: programBookingDetails.sessionType,
        selectedClassIds: programBookingDetails.selectedClassIds,
      });
      setSelectedClasses(programBookingDetails.selectedClasses || []);
    }
  }, [programBookingDetails, programId]);

  // Prepare program estimate request data
  const programEstimateRequestData = useMemo(() => {
    if (!bookingData.sessionType || !programId) return null;

    const payload: any = {
      payment_type: bookingData.sessionType,
      program_id: programId,
      use_wallet: 1,
    };

    if (bookingData.selectedClassIds.length > 0 && bookingData.sessionType === "PER_SESSION") {
      payload["program_class_id"] = bookingData.selectedClassIds;
    }

    return payload;
  }, [bookingData.sessionType, bookingData.selectedClassIds, programId]);

  // Use program booking estimate hook
  const {
    estimateData,
    isLoading: estimateLoading,
    error: estimateError,
  } = useProgramBookingEstimate(programEstimateRequestData);

  // Calculate remaining spots
  const remainingSpots = useMemo(() => {
    if (!publicProgramDetails?.total_max_registrants) return null;
    return publicProgramDetails.total_max_registrants - (publicProgramDetails.total_enrolled || 0);
  }, [publicProgramDetails?.total_max_registrants, publicProgramDetails?.total_enrolled]);

  // Handle session type selection
  const handleSessionTypeSelect = useCallback((sessionType: string) => {
    setBookingData((prev) => ({ ...prev, sessionType }));
  }, []);

  // Handle class selection (toggle)
  const handleClassSelect = useCallback(
    (classId: number) => {
      const programClass = publicProgramDetails?.program_classes?.find(
        (c: any) => c.id === classId
      );
      if (programClass) {
        const classData = {
          id: classId,
          date: programClass.date,
          start_time: programClass.start_time,
          end_time: programClass.end_time,
        };

        setSelectedClasses((prev) => {
          const isAlreadySelected = prev.some((c) => c.id === classId);
          if (isAlreadySelected) {
            // Remove from selection
            return prev.filter((c) => c.id !== classId);
          } else {
            // Add to selection
            return [...prev, classData];
          }
        });

        setBookingData((prev) => {
          const isAlreadySelected = prev.selectedClassIds.includes(classId);
          if (isAlreadySelected) {
            // Remove from selection
            return {
              ...prev,
              selectedClassIds: prev.selectedClassIds.filter((id) => id !== classId),
            };
          } else {
            // Add to selection
            return {
              ...prev,
              selectedClassIds: [...prev.selectedClassIds, classId],
            };
          }
        });
      }
    },
    [publicProgramDetails?.program_classes]
  );

  // Handle booking
  const handleBookNow = useCallback(async () => {
    try {
      setIsBooking(true);
      // Save booking data to store
      setProgramBookingDetails({
        programId: programId,
        sessionType: bookingData.sessionType,
        selectedClassIds: bookingData.selectedClassIds,
        selectedClasses: selectedClasses,
      });

      router.push("/checkout?booking_type=program");
      // TODO: Navigate to checkout or success page
    } catch (error) {
      console.error("Error booking program:", error);
    } finally {
      setIsBooking(false);
    }
  }, [programId, bookingData, selectedClasses]);

  // Show loading state while fetching program details
  if (publicProgramDetailsLoading) {
    return <ProgramDetailsStepSkeleton />;
  }

  // Show error state if program details failed to load
  if (!publicProgramDetails) {
    return (
      <div className="container py-2">
        <div className="flex w-full flex-col items-center justify-center gap-4">
          <div className="text-center text-red-600">Failed to load program details</div>
          <button
            onClick={onBack}
            className="flex items-center gap-2 text-[#1c5534] transition-colors hover:text-[#0f3a1f]"
          >
            <ChevronLeft className="h-8 w-8" />
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-2">
      <div className="flex w-full flex-col items-start justify-start gap-4 lg:gap-10">
        <div className="flex w-full flex-col items-start justify-start gap-4 rounded-[30px] bg-white p-4 lg:flex-row lg:items-start lg:justify-between lg:gap-10 lg:p-10">
          {/* Left Section - Program Details */}
          <div className="flex w-full flex-col items-start justify-start gap-5 lg:w-1/2">
            <button
              onClick={() => {
                onBack();
                clearProgramBooking();
              }}
              className="flex items-center gap-2 text-[#1c5534] transition-colors hover:text-[#0f3a1f]"
            >
              <ChevronLeft className="h-8 w-8" />
            </button>
            {/* Program Card Display */}
            <div className="flex flex-col items-start justify-start self-stretch overflow-hidden rounded-xl bg-white pb-2.5">
              <img
                className="h-[200px] w-full object-cover"
                src={publicProgramDetails?.cover_image || "/imgs/placeholder.svg"}
                alt={publicProgramDetails.name}
              />
              <div className="flex flex-col items-start justify-start gap-2.5 self-stretch px-5 py-2.5">
                <div className="inline-flex items-center justify-start gap-1.5 rounded-2xl bg-[#fffaed] pr-3 pl-2.5 outline outline-offset-[-1px] outline-[#ddba0a]">
                  <div className="relative">
                    <svg
                      width="9"
                      height="8"
                      viewBox="0 0 9 8"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <circle cx="4.5" cy="4" r="3" fill="#1C5534" />
                    </svg>
                  </div>
                  <div className="font-helvetica text-center text-xs leading-[25px] font-normal text-[#1c5534]">
                    {publicProgramDetails?.category?.name || "No category"}
                  </div>
                </div>

                <div className="flex flex-col items-start justify-start gap-[10px]">
                  <div className="font-helvetica text-xl font-bold text-black capitalize">
                    {publicProgramDetails.name || "No name"}
                  </div>

                  <div className="flex flex-col items-start justify-start">
                    <div className="inline-flex items-center justify-start">
                      <div className="relative size-6 overflow-hidden">
                        <div className="absolute top-[3px] left-[3px]">
                          <svg
                            width="18"
                            height="18"
                            viewBox="0 0 18 18"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M15.7258 7.75117H2.67578M12.1008 1.95117V4.85117M6.30078 1.95117V4.85117M6.15578 16.4512H12.2458C13.4639 16.4512 14.073 16.4512 14.5382 16.2141C14.9475 16.0056 15.2802 15.6729 15.4887 15.2636C15.7258 14.7983 15.7258 14.1893 15.7258 12.9712V6.88117C15.7258 5.66306 15.7258 5.054 15.4887 4.58874C15.2802 4.17949 14.9475 3.84676 14.5382 3.63823C14.073 3.40117 13.4639 3.40117 12.2458 3.40117H6.15578C4.93767 3.40117 4.32861 3.40117 3.86335 3.63823C3.4541 3.84676 3.12137 4.17949 2.91284 4.58874C2.67578 5.054 2.67578 5.66306 2.67578 6.88117V12.9712C2.67578 14.1893 2.67578 14.7983 2.91284 15.2636C3.12137 15.6729 3.4541 16.0056 3.86335 16.2141C4.32861 16.4512 4.93767 16.4512 6.15578 16.4512Z"
                              stroke="#1C5534"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </div>
                      </div>
                      <div className="font-helvetica text-sm leading-[25px] font-normal text-black">
                        {publicProgramDetails.start_date && publicProgramDetails.end_date
                          ? formatProgramDateRange(
                              publicProgramDetails.start_date,
                              publicProgramDetails.end_date
                            )
                          : "Date range not available"}
                      </div>
                    </div>

                    <div className="flex w-full items-start justify-start gap-1">
                      <div className="relative size-6 px-[3px]">
                        <div>
                          <svg
                            width="18"
                            height="18"
                            viewBox="0 0 18 18"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M15.7258 8.83867V6.88117C15.7258 5.66306 15.7258 5.054 15.4887 4.58874C15.2802 4.17949 14.9475 3.84676 14.5382 3.63823C14.073 3.40117 13.4639 3.40117 12.2458 3.40117H6.15578C4.93767 3.40117 4.32861 3.40117 3.86335 3.63823C3.4541 3.84676 3.12137 4.17949 2.91284 4.58874C2.67578 5.054 2.67578 5.66306 2.67578 6.88117V12.9712C2.67578 14.1893 2.67578 14.7983 2.91284 15.2636C3.12137 15.6729 3.4541 16.0056 3.86335 16.2141C4.32861 16.4512 4.93767 16.4512 6.15578 16.4512H9.56328M15.7258 7.75117H2.67578M12.1008 1.95117V4.85117M6.30078 1.95117V4.85117M13.5508 15.7262V11.3762M11.3758 13.5512H15.7258"
                              stroke="#1C5534"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </div>
                      </div>
                      <div className="font-helvetica line-clamp-2 justify-start text-sm leading-[25px] font-normal text-black">
                        {publicProgramDetails.program_classes &&
                        publicProgramDetails.program_classes.length > 0
                          ? formatDateList(
                              publicProgramDetails.program_classes
                                .filter((c: any) => c.status === 1 && new Date(c.date) > new Date())
                                .sort((a: any, b: any) => {
                                  return new Date(a.date).getTime() - new Date(b.date).getTime();
                                })
                                .map((c: any) => c.date)
                            )
                          : "No dates available"}
                      </div>
                    </div>

                    <div className="inline-flex items-center justify-start">
                      <div className="relative size-6 overflow-hidden">
                        <div className="absolute top-[3px] left-[3px]">
                          <svg
                            width="18"
                            height="18"
                            viewBox="0 0 18 18"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M9.19922 4.8502V9.2002L12.0992 10.6502M16.4492 9.2002C16.4492 13.2043 13.2033 16.4502 9.19922 16.4502C5.19516 16.4502 1.94922 13.2043 1.94922 9.2002C1.94922 5.19613 5.19516 1.9502 9.19922 1.9502C13.2033 1.9502 16.4492 5.19613 16.4492 9.2002Z"
                              stroke="#1C5534"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </div>
                      </div>
                      <div className="font-helvetica text-sm leading-[25px] font-normal text-black">
                        {publicProgramDetails.start_time && publicProgramDetails.end_time
                          ? formatProgramTimeRange(
                              publicProgramDetails.start_time,
                              publicProgramDetails.end_time
                            )
                          : "Time not available"}
                      </div>
                    </div>

                    <div className="inline-flex items-center justify-start">
                      <div className="relative size-6 overflow-hidden">
                        <div className="absolute top-[3px] left-[3px]">
                          <svg
                            width="18"
                            height="18"
                            viewBox="0 0 18 18"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M9.19922 1.9502C13.2033 1.9502 16.4492 5.19613 16.4492 9.2002C16.4492 13.2043 13.2033 16.4502 9.19922 16.4502C5.19516 16.4502 1.94922 13.2043 1.94922 9.2002C1.94922 5.19613 5.19516 1.9502 9.19922 1.9502ZM9.19922 5.5752C8.78501 5.5752 8.44922 5.91099 8.44922 6.3252V9.2002C8.44922 9.61441 8.78501 9.9502 9.19922 9.9502H12.0742C12.4884 9.9502 12.8242 9.61441 12.8242 9.2002C12.8242 8.78599 12.4884 8.4502 12.0742 8.4502H9.94922V6.3252C9.94922 5.91099 9.61343 5.5752 9.19922 5.5752Z"
                              fill="#1C5534"
                            />
                          </svg>
                        </div>
                      </div>
                      <div className="font-helvetica text-sm font-normal text-black">
                        {remainingSpots !== null
                          ? `${remainingSpots} of ${publicProgramDetails.total_max_registrants} spots remaining`
                          : `${publicProgramDetails?.total_enrolled || 0} enrolled (no limit)`}
                      </div>
                    </div>
                  </div>

                  {/* <div className="inline-flex items-center justify-center gap-2.5 rounded-[10px] px-[5px] py-0.5 outline outline-offset-[-1px] outline-[#c3c3c3]">
                    <div className="font-helvetica text-[25px] font-bold text-[#1c5534]">
                      ${publicProgramDetails?.subscribed_program_price?.full_price || "N/A"}
                    </div>
                  </div> */}
                </div>
              </div>
            </div>
          </div>
          {/* Divider - Hidden on mobile, visible on desktop */}
          <div
            className="hidden h-full w-[2px] rounded-full bg-[#EBEBEB] lg:block"
            data-svg-wrapper
          ></div>
          {/* Right Section - Booking Form */} {/* Right Section - Booking Form */}
          <div className="flex w-full flex-col items-start justify-start gap-3 lg:w-1/2">
            {/* Session Type Selector */}
            <div className="font-helvetica w-full text-base font-normal text-black">
              Type of Session
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-6 py-2 transition-colors hover:bg-gray-50">
                <span className="font-helvetica text-[15px] leading-[35px] font-normal text-black">
                  {bookingData.sessionType
                    ? sessionTypes.find((s) => s.id === bookingData.sessionType)?.name ||
                      "Select session type"
                    : "Select session type"}
                </span>
                <ChevronDown className="h-4 w-4" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] min-w-[var(--radix-dropdown-menu-trigger-width)] rounded-[20px] border border-[#c3c3c3] bg-white shadow-lg">
                {sessionTypes.map((sessionType) => (
                  <DropdownMenuItem
                    key={sessionType.id}
                    onClick={() => handleSessionTypeSelect(sessionType.id)}
                    className="cursor-pointer px-[25px] py-3 transition-colors hover:bg-gray-50"
                  >
                    <div className="flex flex-col">
                      <span className="font-helvetica text-[15px] font-normal text-black">
                        {sessionType.name}
                      </span>
                      <span className="font-helvetica text-xs font-normal text-[#c3c3c3]">
                        {sessionType.description}
                      </span>
                    </div>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Program Classes Selector */}
            {bookingData.sessionType === "PER_SESSION" && (
              <>
                <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
                  Select Date
                </div>
                <Popover open={classPopoverOpen} onOpenChange={setClassPopoverOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="font-helvetica flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-[25px] py-6 text-[15px] leading-[35px] font-normal text-black transition-colors hover:bg-gray-50"
                    >
                      {selectedClasses.length > 0
                        ? selectedClasses.length === 1
                          ? `${new Date(selectedClasses[0].date).toLocaleDateString("en-US", {
                              month: "short",
                              day: "numeric",
                            })} (${formatProgramTimeRange(selectedClasses[0].start_time, selectedClasses[0].end_time)})`
                          : `${selectedClasses.length} dates selected`
                        : "Select dates"}
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent
                    className="max-h-[420px] w-[var(--radix-popover-trigger-width)] max-w-[90vw] overflow-scroll rounded-[20px] border border-[#c3c3c3] bg-white p-4 shadow-lg"
                    align="start"
                  >
                    <div className="grid grid-cols-2 gap-2 sm:grid-cols-3 md:grid-cols-4">
                      {publicProgramDetails.program_classes
                        ?.sort((a: any, b: any) => {
                          return new Date(a.date).getTime() - new Date(b.date).getTime();
                        })
                        .map((programClass: any, index: number) => {
                          const classId = programClass.id;
                          const isSelected = selectedClasses.some((c) => c.id === classId);

                          console.log("is previous data", new Date(programClass.date) < new Date());

                          return (
                            <button
                              key={index}
                              onClick={() => handleClassSelect(classId)}
                              disabled={
                                programClass?.status === 0 ||
                                new Date(programClass.date) < new Date()
                              }
                              className={`font-helvetica relative cursor-pointer rounded-[12px] border px-3 py-2 text-sm font-normal transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50 ${
                                isSelected
                                  ? "border-[#1c5534] bg-[#1c5534] text-white"
                                  : "border-[#c3c3c3] bg-white text-black hover:border-[#1c5534] hover:bg-gray-50"
                              }`}
                            >
                              <div className="flex flex-col items-center">
                                <span className="text-[13px] leading-tight">
                                  {new Date(programClass.date).toLocaleDateString("en-US", {
                                    month: "short",
                                    day: "numeric",
                                    year: "numeric",
                                  })}
                                </span>
                                {/* <span className="text-[11px] leading-tight opacity-75">
                                  {formatProgramTimeRange(
                                    programClass.start_time,
                                    programClass.end_time
                                  )}
                                </span> */}
                              </div>
                              {isSelected && (
                                <div className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-[#ddba0a]"></div>
                              )}
                            </button>
                          );
                        })}
                    </div>
                  </PopoverContent>
                </Popover>
              </>
            )}

            {/* Booking Summary */}
            <div className="mt-6 flex flex-col items-start justify-start gap-5 self-stretch">
              <div className="inline-flex items-end justify-between self-stretch rounded-[20px] bg-[#fffaed] px-6 pt-2.5 pb-[30px] shadow-[0px_-4px_20px_0px_rgba(0,0,0,0.06)]">
                <div className="w-full justify-start">
                  <span className="font-helvetica text-xl leading-7 font-medium text-black">
                    Booking Details
                    <br />
                  </span>
                  <span className="font-helvetica text-lg leading-[25.20px] font-normal text-black">
                    {publicProgramDetails.name}
                    <br />
                    {bookingData.sessionType
                      ? sessionTypes.find((s) => s.id === bookingData.sessionType)?.name ||
                        "No session selected"
                      : "No session selected"}
                    <br />
                    {bookingData.sessionType === "PER_SESSION" && selectedClasses.length > 0
                      ? selectedClasses.length === 1
                        ? new Date(selectedClasses[0].date).toLocaleDateString("en-US", {
                            weekday: "short",
                            day: "numeric",
                            month: "long",
                          }) +
                          ` (${formatProgramTimeRange(selectedClasses[0].start_time, selectedClasses[0].end_time)})`
                        : `${selectedClasses.length} dates selected`
                      : publicProgramDetails.start_date
                        ? formatProgramDateRange(
                            publicProgramDetails.start_date,
                            publicProgramDetails.end_date || publicProgramDetails.start_date
                          )
                        : "Date TBD"}
                    <br />
                    {remainingSpots !== null
                      ? `${remainingSpots} spots remaining`
                      : "Unlimited spots"}
                    <br />
                  </span>

                  {/* Price Estimation */}
                  {estimateLoading ? (
                    <div className="mt-4 flex items-center gap-2">
                      <Loader />
                      <span className="font-helvetica text-sm text-gray-600">
                        Calculating price...
                      </span>
                    </div>
                  ) : estimateData ? (
                    <div className="mt-4 space-y-2">
                      {estimateData.tax_amount > 0 && (
                        <div className="flex items-center justify-between">
                          <span className="font-helvetica text-sm text-gray-600">Tax</span>
                          <span className="font-helvetica text-sm text-gray-600">
                            ${estimateData.tax_amount}
                          </span>
                        </div>
                      )}
                      <div className="flex items-center justify-between border-t border-gray-300 pt-2">
                        <span className="font-helvetica text-lg font-bold text-black">Total</span>
                        <span className="font-helvetica text-lg font-bold text-[#1c5534]">
                          ${estimateData.total}
                        </span>
                      </div>
                    </div>
                  ) : estimateError ? (
                    <div className="mt-4 text-sm text-red-600">
                      Error calculating price: {estimateError}
                    </div>
                  ) : null}
                </div>
              </div>

              {/* Enroll Button */}
              <button
                onClick={handleBookNow}
                disabled={
                  !bookingData.sessionType ||
                  isBooking ||
                  estimateLoading ||
                  (bookingData.sessionType === "PER_SESSION" && selectedClasses.length === 0)
                }
                className="font-helvetica text-primary flex w-full items-center justify-center rounded-full bg-[#ddba0a] px-10 py-4 text-center text-base leading-none font-bold shadow-[0px_4px_20px_0px_rgba(0,0,0,0.06)] transition-colors hover:bg-[#c4a609] disabled:cursor-not-allowed disabled:opacity-50"
              >
                {isBooking ? <Loader /> : "Enroll Now"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgramDetailsStep;
