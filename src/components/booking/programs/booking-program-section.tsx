"use client";
import { useGetPublicProgramList } from "@/api/booking-program-service";
import { useBookingStore } from "@/store/booking-store";
import { useCallback, useState, useEffect } from "react";
import ProgramCard from "./program-card";
import ProgramCardSkeleton from "./program-card-skeleton";
import ProgramDetailsStep from "./program-details-step";

export interface Program {
  id: number;
  name: string;
  cover_image: string;
  description: string;
  category?: {
    name: string;
  };
  start_date?: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  total_max_registrants?: number;
  total_enrolled?: number;
  subscribed_program_price: {
    per_class_price: number;
    full_price: number;
  };
  class_dates?: string[];
  // Add other program properties as needed
}

const BookingProgramSection = ({ locationId }: { locationId: number }) => {
  // locationId is available for future filtering by location
  const [currentStep, setCurrentStep] = useState<"list" | "details">("list");
  const [selectedProgramId, setSelectedProgramId] = useState<number | null>(null);

  // Get booking store functions
  const {
    setSelectedProgram: setBookingProgram,
    clearProgramBooking,
    programBookingDetails,
  } = useBookingStore();

  const {
    publicProgramList,
    publicProgramListLoading,
    publicProgramListError,
    publicProgramListEmpty,
  } = useGetPublicProgramList({ page: 1, limit: 20 });

  console.log("publicProgramList", publicProgramList);

  // Show details step when returning from checkout with existing programBookingDetails
  useEffect(() => {
    if (programBookingDetails && programBookingDetails.programId) {
      setSelectedProgramId(programBookingDetails.programId);
      setCurrentStep("details");
    }
  }, [programBookingDetails]);

  const handleBookNow = useCallback(
    (program: Program) => {
      setSelectedProgramId(program.id);
      setCurrentStep("details");
    },
    [setBookingProgram]
  );

  const handleBackToList = useCallback(() => {
    setCurrentStep("list");
    setSelectedProgramId(null);
    // Clear program booking data when going back to list
    clearProgramBooking();
  }, [clearProgramBooking]);

  //   if (currentStep === "details" && selectedProgram) {
  //     return <ProgramDetailsStep program={selectedProgram} onBack={handleBackToList} />;
  //   }

  return (
    <div className="container mx-auto px-4 py-1">
      <div className="inline-flex w-full flex-col items-center justify-start">
        <div className="font-helvetical justify-center self-stretch text-center text-[25px] leading-[25px] font-bold text-black">
          Choose Your Program
        </div>
        <div className="font-helvetical max-w-2xl justify-center text-center text-[15px] leading-relaxed font-normal text-[#364153]">
          Each weekly session develops your technique, strategy, and consistency through game-like
          drills, with programs available in various cities to suit your goals and schedule.
        </div>
      </div>

      {currentStep !== "details" || !selectedProgramId ? (
        <div className="mt-4">
          {publicProgramListLoading ? (
            <div className="grid grid-cols-1 items-center justify-items-center gap-6 md:grid-cols-2 xl:grid-cols-3">
              {Array.from({ length: 6 }).map((_, index) => (
                <ProgramCardSkeleton key={index} />
              ))}
            </div>
          ) : publicProgramListError ? (
            <div>Error: {publicProgramListError.message}</div>
          ) : publicProgramListEmpty ? (
            <div>No programs available</div>
          ) : (
            <div className="grid grid-cols-1 items-center justify-items-center gap-6 md:grid-cols-2 xl:grid-cols-3">
              {publicProgramList.map((program: Program) => (
                <ProgramCard key={program.id} program={program} onBookNow={handleBookNow} />
              ))}
            </div>
          )}
        </div>
      ) : (
        <ProgramDetailsStep programId={selectedProgramId} onBack={handleBackToList} />
      )}
    </div>
  );
};

export default BookingProgramSection;
