"use client";
import LoginDialog from "@/components/auth/login-dialog";
import { Button } from "@/components/ui/button";
import BookingTypeSelector from "./booking-type-selector";
import SportSelector from "./sport-selector";

import { useGetPublicCourtLocations } from "@/api/booking-service";
import { useAuthStore } from "@/store/auth-store";
import { useUIStore } from "@/store/ui-store";
import { BookingType } from "@/types/booking";
import { CircleUserRound } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useMemo } from "react";
import BookingSectionSkeleton from "./booking-section-skeleton";

interface BookingSectionProps {
  locationSlug: string;
  isFromBookingPage?: boolean;
}

const BookingSection = ({ locationSlug, isFromBookingPage = false }: BookingSectionProps) => {
  const searchParams = useSearchParams();
  const bookingType = (searchParams.get("booking_type") as BookingType) || "court";

  const { publicCourtLocationList, publicCourtLocationListLoading } = useGetPublicCourtLocations();

  const firstCourtId = useMemo(() => {
    return publicCourtLocationList && publicCourtLocationList.length > 0
      ? publicCourtLocationList[0].id
      : null;
  }, [publicCourtLocationList, publicCourtLocationListLoading]);

  const { isAuthenticated, isLoading } = useAuthStore();
  const { openLoginDialog } = useUIStore();

  const handleLoginClick = () => {
    openLoginDialog();
  };

  return (
    <div className="flex w-full flex-col items-center">
      {!isFromBookingPage && (
        <div className="w-full flex-col items-center justify-start">
          <h1 className="font-helvetica justify-center text-center text-2xl font-bold text-[#1c5534] md:text-3xl">
            Booking Options
          </h1>
          <p className="font-helvetica justify-center self-stretch text-center font-normal text-[#364153]">
            Choose how you play. Book the experience that fits you.
          </p>
        </div>
      )}

      {/* Show loading state while checking authentication */}
      {isLoading ? (
        <div className="flex w-full items-center justify-center py-8">
          <BookingSectionSkeleton />
        </div>
      ) : isAuthenticated ? (
        // Show booking options for authenticated users
        <>
          {!isFromBookingPage && <SportSelector />}
          {firstCourtId && (
            <BookingTypeSelector
              locationId={firstCourtId}
              bookingType={bookingType}
              isFromBookingPage={isFromBookingPage}
            />
          )}
        </>
      ) : (
        // Show login button for non-authenticated users
        <div className="flex flex-col items-center justify-center space-y-4 py-8">
          <Button
            onClick={handleLoginClick}
            className="bg-[#1c5534] px-8 py-6 text-lg font-semibold text-white hover:bg-[#1c5534]/90"
          >
            <CircleUserRound className="mr-2 h-10 w-10" />
            <span>Log In to Book</span>
          </Button>
        </div>
      )}

      {/* Login Dialog */}
      <LoginDialog />
    </div>
  );
};

export default BookingSection;
