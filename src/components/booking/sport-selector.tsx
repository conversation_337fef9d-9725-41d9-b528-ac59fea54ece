import { useBookingStore } from "@/store/booking-store";
import { SportType } from "@/types/booking";
import React, { useCallback } from "react";

const SportSelector = () => {
  const { selectedSport, setSelectedSport } = useBookingStore();

  const handleSportSelect = useCallback(
    (sport: SportType) => {
      setSelectedSport(sport);
    },
    [setSelectedSport]
  );

  const sportItems = [
    {
      name: "Padel",
      image: "/imgs/booking/padel.png",
      comingSoon: false,
    },
    {
      name: "Pickleball",
      image: "/imgs/booking/pickleball.png",
      comingSoon: true,
    },
    {
      name: "<PERSON>",
      image: "/imgs/booking/tennis.png",
      comingSoon: true,
    },
  ];
  return (
    <div className="container flex w-full items-center justify-start gap-2 py-4 sm:gap-6 md:gap-10 md:py-8">
      {sportItems.map((sport) => (
        <div
          key={sport.name}
          aria-disabled={sport.comingSoon}
          className="relative flex h-16 flex-1 cursor-pointer items-center justify-center gap-2.5 rounded-[10px] p-2 transition-all duration-200 disabled:cursor-not-allowed sm:h-20 md:h-[92px] md:p-2.5"
          onClick={() => handleSportSelect(sport.name as SportType)}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              handleSportSelect(sport.name as SportType);
            }
          }}
        >
          {/* Background layer with opacity */}
          <div
            className="absolute inset-0 rounded-[10px] transition-all duration-500"
            style={{
              backgroundImage: `url(${sport.image})`,
              backgroundRepeat: "no-repeat",
              objectFit: "cover",
              backgroundSize: "cover",
              backgroundPosition: "center",
              opacity: selectedSport === sport.name ? 1 : 0.1,
              backgroundBlendMode: "luminosity",
              backgroundColor: selectedSport === sport.name ? "transparent" : "#F5F5F5",
            }}
          />

          <div
            className={`absolute inset-0 rounded-[10px] transition-all duration-500 ${selectedSport === sport.name ? "bg-[#1c5534]/20 opacity-100" : "bg-[#F5F5F5] opacity-10"}`}
          />

          {/* Text layer with full opacity */}
          <div className="relative z-10 text-center">
            <p
              className={`text-sm font-bold sm:text-lg md:text-2xl ${
                selectedSport === sport.name ? "text-white" : "text-[#9F9F9F]"
              }`}
            >
              {sport.name}
            </p>
            {sport.comingSoon && (
              <p
                className={`text-xs sm:text-sm ${
                  selectedSport === sport.name ? "text-white" : "text-[#9F9F9F]"
                }`}
              >
                Coming Soon
              </p>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default SportSelector;
