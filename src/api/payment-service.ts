import useSWR, { mutate } from "swr";
import { useMemo } from "react";
import axiosInstance, { endpoints, epicFetcher } from "./axios";

export function useGetPaymentMethods() {

    const fullUrl = useMemo(
        () => `${endpoints.payment.getList}`,
        []
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidatePaymentMethods = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || [];
        return {
            paymentMethodList: queueData,
            paymentMethodLoading: isLoading,
            paymentMethodError: error,
            paymentMethodValidating: isValidating,
            paymentMethodEmpty: queueData.length === 0,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidatePaymentMethods,
    };
}

export function setDefaultPaymentMethod({ paymentMethodId }: { paymentMethodId: string }): Promise<any> {
    const response = axiosInstance.get(`${endpoints.payment.setDefault}/${paymentMethodId}`);
    return response;
}

export async function checkoutTransactionStatus({ transactionId }: { transactionId: string | null }): Promise<any> {
    const response = await axiosInstance.get(`/public/auth/checkoutTransactionStatus?transaction_id=${transactionId}`);
    console.log("checkout transaction status response", response);
    return response;
}

export async function createSetupIntent(): Promise<any> {
    const response = await axiosInstance.post(endpoints.payment.setupIntent);
    return response;
}

export async function addPaymentMethod({ paymentMethodId }: { paymentMethodId: string }): Promise<any> {
    const response = await axiosInstance.post(endpoints.payment.addPayment, { payment_method_id: paymentMethodId });
    console.log("add payment method response", response);
    return response;
}

export async function deletePaymentMethod({ paymentMethodId }: { paymentMethodId: string }) {
    const response = await axiosInstance.delete(`${endpoints.payment.deletePayment}/${paymentMethodId}`);
    return response;
}
