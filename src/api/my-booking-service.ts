import { useMemo } from "react";
import useSWR, { mutate } from "swr";
import { endpoints, epicFetcher } from "./axios";

export function useGetMyCourtBookingList({ page, limit }: { page: number; limit: number }) {

    const fullUrl = useMemo(
        () => {
            const params = new URLSearchParams();
            params.append("page", page.toString());
            params.append("limit", limit.toString());
            return `${endpoints.myBooking.getCourtBookingList}?${params.toString()}`;
        },
        []
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetMyCourtBookingList = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || [];
        return {
            myCourtBookingList: queueData,
            myCourtBookingListLoading: isLoading,
            myCourtBookingListError: error,
            myCourtBookingListValidating: isValidating,
            myCourtBookingListEmpty: queueData.length === 0,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetMyCourtBookingList,
    };
}