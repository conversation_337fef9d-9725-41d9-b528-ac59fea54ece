import { useMemo } from "react";
import useSWR, { mutate } from "swr";
import { endpoints, epicFetcher } from "./axios";

export function useGetPublicInstructorList({ page, limit, lessonTypeId }: { page: number; limit: number, lessonTypeId?: number }) {

    const fullUrl = useMemo(
        () => {
            const params = new URLSearchParams();
            params.append("page", page.toString());
            params.append("limit", limit.toString());
            if (lessonTypeId) {
                params.append("lesson_type_id", lessonTypeId.toString());
            }
            return `${endpoints.booking.getPublicInstructorList}?${params.toString()}`;
        },
        []
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetPublicInstructorList = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || [];
        return {
            publicInstructorList: queueData,
            publicInstructorListLoading: isLoading,
            publicInstructorListError: error,
            publicInstructorListValidating: isValidating,
            publicInstructorListEmpty: queueData.length === 0,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetPublicInstructorList,
    };
}