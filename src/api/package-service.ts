import useSWR, { mutate } from "swr";
import { useMemo } from "react";
import axiosInstance, { endpoints, epicFetcher } from "./axios";
import { Plan } from "@/types/membership";

interface PlansResponse {
    data: Plan[];
}



export function useGetPackages() {

    const fullUrl = useMemo(
        () => `${endpoints.public.packages}`,
        []
    );

    const { data, error, isLoading, isValidating } = useSWR<PlansResponse | null>(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidatePackages = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || [];
        return {
            packageList: queueData,
            packageListLoading: isLoading,
            packageListError: error,
            packageListValidating: isValidating,
            packageListEmpty: queueData.length === 0,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidatePackages,
    };
}