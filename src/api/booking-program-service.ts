import { useMemo } from "react";
import useSWR, { mutate } from "swr";
import axiosInstance, { endpoints, epicFetcher } from "./axios";

export function useGetPublicProgramList({ page, limit }: { page: number; limit: number }) {

    const fullUrl = useMemo(

        () => `${endpoints.booking.getAllPublicPrograms}?page=${page}&limit=${limit}`,
        []
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetPublicProgramList = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || [];
        return {
            publicProgramList: queueData,
            publicProgramListLoading: isLoading,
            publicProgramListError: error,
            publicProgramListValidating: isValidating,
            publicProgramListEmpty: queueData.length === 0,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetPublicProgramList,
    };
}

export function useGetPublicProgramDetailsById({ programId }: { programId: number }) {

    const fullUrl = useMemo(

        () => `${endpoints.booking.getPublicProgramById}?id=${programId}`,
        []
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetPublicProgramDetails = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || [];
        return {
            publicProgramDetails: queueData,
            publicProgramDetailsLoading: isLoading,
            publicProgramDetailsError: error,
            publicProgramDetailsValidating: isValidating,
            publicProgramDetailsEmpty: queueData.length === 0,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetPublicProgramDetails,
    };
}

export async function createProgramBooking(bookingData: any): Promise<any> {
    const response = await axiosInstance.post(endpoints.booking.createProgramBooking, bookingData);
    return response;
}

export async function confirmProgramCheckout(checkoutData: any): Promise<any> {
    const response = await axiosInstance.post(endpoints.booking.confirmProgramCheckout, checkoutData);
    return response;
}

export async function confirmApplePayProgramCheckout(checkoutData: any): Promise<any> {
    const response = await axiosInstance.post(endpoints.checkout.applePayProgramConfirm, checkoutData);
    return response;
}