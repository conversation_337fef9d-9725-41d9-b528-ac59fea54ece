import { useMemo } from "react";
import useSWR, { mutate } from "swr";
import axiosInstance, { endpoints, epicFetcher } from "./axios";

export function useGetPublicLessonList({ page, limit }: { page: number; limit: number }) {

    const fullUrl = useMemo(

        () => `${endpoints.booking.getPublicLessonList}?page=${page}&limit=${limit}`,
        []
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetPublicLessonList = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data?.data || [];
        return {
            publicLessonList: queueData,
            publicLessonListLoading: isLoading,
            publicLessonListError: error,
            publicLessonListValidating: isValidating,
            publicLessonListEmpty: queueData.length === 0,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetPublicLessonList,
    };
}