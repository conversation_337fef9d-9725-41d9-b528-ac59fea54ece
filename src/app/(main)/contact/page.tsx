"use client";

import { sendConversionAPIEvent } from "@/api/meta-conversion-api-service";
import { useFacebookPixel } from "@/hooks/use-facebook-pixel";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useRef, useState } from "react";
import { toast } from "sonner";
import { sendContactEmail } from "../actions/contact";

// Register GSAP plugins
if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

export default function Contact() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const headingRef = useRef<HTMLHeadingElement>(null);
  const formContainerRef = useRef<HTMLDivElement>(null);
  const contactInfoRef = useRef<HTMLDivElement>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const formRef = useRef<HTMLFormElement>(null);
  const { trackContact } = useFacebookPixel();

  async function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault();
    setIsSubmitting(true);

    try {
      const formData = new FormData(event.currentTarget);
      const result = await sendContactEmail(formData);

      if (result.success) {
        toast.success(result.message);
        formRef.current?.reset();
        trackContact();
        // sendFacebookConversionApiEvent("Contact", {
        //   email: "<EMAIL>",
        //   phone: "0123123123",
        // });
        sendConversionAPIEvent({ eventName: "Contact" });
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error sending email:", error);
      toast.error("An unexpected error occurred. Please try again later.");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <>
      <main className="main">
        <section className="section block-contact is-mode bg-0" id="1" ref={sectionRef}>
          <div className="block-map pt-18 md:pt-22">
            <iframe
              src="https://snazzymaps.com/embed/609243"
              width="100%"
              height="462px"
              style={{ border: "none" }}
              loading="lazy"
            ></iframe>
          </div>
          <div className="container mx-auto px-4 py-16">
            <h2
              ref={headingRef}
              className="heading-2 color-brand-2 grow-up scroll-zoom-in text-4xl font-bold text-[#1c5534] lg:text-6xl"
            >
              Get in touch
            </h2>
            <div className="row mt-20 grid grid-cols-1 gap-8 lg:grid-cols-12">
              <div className="col-lg-4 lg:col-span-4">
                <div className="card-contact" ref={contactInfoRef}>
                  <div className="contact-info">
                    <h4 className="color-900 card-title mb-4 text-2xl font-semibold text-gray-900">
                      Headquarters
                    </h4>
                    <p className="font-md color-600 text-gray-600">
                      Arlington, VA <br />
                      USA
                    </p>
                  </div>
                </div>
              </div>
              <div className="col-lg-8 lg:col-span-8">
                <div className="form-in-touch position-relative" ref={formContainerRef}>
                  <div className="image-mark-scroll"></div>
                  <form id="contact-form" ref={formRef} onSubmit={handleSubmit}>
                    <div className="row grid grid-cols-1 gap-6 lg:grid-cols-2">
                      <div className="col-lg-6">
                        <div className="form-group">
                          <input
                            className="form-control w-full rounded-xl border border-gray-300 bg-white px-4 py-6 focus:border-transparent focus:ring-2 focus:ring-[#1c5534] focus:outline-none"
                            type="text"
                            id="full-name"
                            name="full-name"
                            placeholder="Full Name"
                            required
                          />
                        </div>
                      </div>
                      <div className="col-lg-6">
                        <div className="form-group">
                          <input
                            className="form-control w-full rounded-xl border border-gray-300 bg-white px-4 py-6 focus:border-transparent focus:ring-2 focus:ring-[#1c5534] focus:outline-none"
                            type="tel"
                            id="phone-number"
                            maxLength={10}
                            name="phone-number"
                            placeholder="Phone Number"
                            onChange={(e) => {
                              const value = e.target.value.replace(/\D/g, "");
                              if (value.length <= 10) {
                                e.target.value = value;
                              }
                            }}
                          />
                        </div>
                      </div>
                      <div className="col-lg-6">
                        <div className="form-group">
                          <input
                            className="form-control w-full rounded-xl border border-gray-300 bg-white px-4 py-6 focus:border-transparent focus:ring-2 focus:ring-[#1c5534] focus:outline-none"
                            type="email"
                            id="email-address"
                            name="email-address"
                            placeholder="Email Address"
                            required
                          />
                        </div>
                      </div>
                      <div className="col-lg-6">
                        <div className="form-group">
                          <input
                            className="form-control w-full rounded-xl border border-gray-300 bg-white px-4 py-6 focus:border-transparent focus:ring-2 focus:ring-[#1c5534] focus:outline-none"
                            type="text"
                            id="subject"
                            name="subject"
                            placeholder="Subject"
                            required
                          />
                        </div>
                      </div>
                      <div className="col-lg-12">
                        <div className="form-group">
                          <textarea
                            className="form-control w-full rounded-xl border border-gray-300 bg-white px-4 py-6 focus:border-transparent focus:ring-2 focus:ring-[#1c5534] focus:outline-none"
                            id="message"
                            name="message"
                            rows={6}
                            placeholder="Message"
                            required
                          ></textarea>
                        </div>
                      </div>
                      <div className="col-lg-12">
                        <div className="box-button-touch">
                          <div className="form-group">
                            <button
                              className="btn btn-black mr-15 rounded-full bg-[#1c5534] px-8 py-4 text-xl font-bold text-white transition-colors duration-300 hover:bg-[#16432a] disabled:cursor-not-allowed disabled:opacity-50"
                              id="submit-btn"
                              type="submit"
                              disabled={isSubmitting}
                            >
                              {isSubmitting ? "Sending..." : "Send Message"}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "ProgramMembership",
            name: "Epic Padel Incubation Program",
            url: "https://epic-padel.com/incubation",
            programName: "Epic Padel Startup Incubator",
            hostingOrganization: {
              "@type": "Organization",
              name: "Epic Padel",
              url: "https://epic-padel.com/",
              logo: "https://epic-padel.com/imgs/template/logo-green-small.svg",
              address: {
                "@type": "PostalAddress",
                streetAddress: "4112 Cherry Hill Rd",
                addressLocality: "Arlington",
                addressRegion: "VA",
                postalCode: "22207",
                addressCountry: "US",
              },
            },
            membershipNumber: "Optional",
            member: {
              "@type": "Audience",
              audienceType: "Startups, Entrepreneurs, Sports Tech Innovators",
            },
            membershipPointsEarned: {
              "@type": "QuantitativeValue",
              value: 0,
            },
            additionalProperty: [
              {
                "@type": "PropertyValue",
                name: "Business mentoring",
                value: "Included",
              },
              {
                "@type": "PropertyValue",
                name: "Access to facilities",
                value: "Included",
              },
              {
                "@type": "PropertyValue",
                name: "Funding opportunities",
                value: "Available",
              },
              {
                "@type": "PropertyValue",
                name: "Partnership exposure",
                value: "Included",
              },
            ],
          }),
        }}
      />
    </>
  );
}
