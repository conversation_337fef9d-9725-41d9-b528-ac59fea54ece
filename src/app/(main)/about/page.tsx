"use client";

import React, { useEffect, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Register GSAP plugins
if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

const AboutPage = () => {
  const sectionRefs = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    // Initialize animations
    sectionRefs.current.forEach((section, index) => {
      if (section) {
        // Fade in animation for sections
        gsap.fromTo(
          section,
          {
            opacity: 0,
            y: 50,
          },
          {
            opacity: 1,
            y: 0,
            duration: 1,
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              end: "bottom 20%",
              toggleActions: "play none none reverse",
            },
          }
        );

        // Parallax effect for images
        const images = section.querySelectorAll("img");
        images.forEach((img) => {
          gsap.to(img, {
            yPercent: 30,
            ease: "none",
            scrollTrigger: {
              trigger: img,
              start: "top bottom",
              end: "bottom top",
              scrub: true,
            },
          });
        });
      }
    });
  }, []);

  return (
    <main className="bg-[#fffaef]">
      {/* Who We Are Section */}
      <section
        ref={(el: HTMLDivElement | null) => {
          sectionRefs.current[0] = el;
        }}
        className="py-20 md:py-32"
        id="1"
      >
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 gap-12 lg:grid-cols-2">
            <div className="text-center lg:text-left">
              <h2 className="mt-16 mb-8 text-4xl font-semibold text-[#1c5534] md:text-5xl">
                Who we are?
              </h2>
              <p className="mb-8 text-lg text-gray-800 opacity-80 md:text-xl">
                Welcome to EPIC, a leading padel sports management and investment company
                headquartered in Arlington, VA. Our mission is to become the leading operator of
                padel clubs across North America. As padel gains rapid growth worldwide, we're ready
                to capitalize on the early-stage popularity in North America.
              </p>
              <Image
                className="w-full rounded-lg object-cover opacity-80 lg:pt-48"
                src="/imgs/page/portfolio5/img.png"
                alt="EPIC Padel"
                width={500}
                height={300}
                loading="lazy"
              />
            </div>
            <div>
              <div className="mb-8 text-right">
                <Image
                  className="w-full rounded-lg opacity-80"
                  src="/imgs/page/portfolio5/img2.png"
                  alt="EPIC Vision"
                  width={500}
                  height={300}
                  loading="lazy"
                />
              </div>
              <div className="pt-24">
                <h2 className="mb-8 text-4xl font-semibold text-[#1c5534] md:text-5xl">
                  Our Vision
                </h2>
                <div>
                  <p className="mb-8 text-lg text-gray-800 opacity-80 md:text-xl">
                    At EPIC, we envision initiating a new era in padel sports —one defined by
                    inclusivity, women empowerment, sustainability, and cutting-edge technology.
                    From day one, our investment strategy has focused on partnering with like-minded
                    operators and padel-related businesses.
                  </p>
                  <div className="mt-8">
                    <Link
                      href="/investments"
                      className="inline-flex items-center rounded-full bg-[#ddba0a] px-8 py-4 font-bold text-[#fffaef] transition-colors hover:bg-[#c4a809]"
                    >
                      Read more
                    </Link>
                  </div>
                </div>
                <div className="pt-16">
                  <p className="mb-8 text-lg text-gray-800 opacity-80 md:text-xl">
                    Additionally, EPIC is committed to incubating innovative ideas that enhance the
                    experiences of our society members. In collaboration with Volee, an all-in-one
                    padel app offering court bookings, matchmaking, NFTs, and crypto collectibles,
                    we're revolutionizing the padel experience.
                  </p>
                  <div className="mt-8">
                    <Link
                      href="/incubation"
                      className="inline-flex items-center rounded-full bg-[#ddba0a] px-8 py-4 font-bold text-[#fffaef] transition-colors hover:bg-[#c4a809]"
                    >
                      Discover more
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Offering Section */}
      <section
        ref={(el: HTMLDivElement | null) => {
          sectionRefs.current[1] = el;
        }}
        className="bg-background py-20 md:py-32"
        id="2"
      >
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
            <div>
              <h1 className="mb-8 text-4xl font-semibold text-[#1c5534] md:text-5xl">
                Our Offering
              </h1>
              <p className="mt-16 text-lg text-gray-800 md:text-xl">
                <span className="font-bold">EPIC Venues:</span> Our flagship product comprises
                state-of-the-art Indoor EPIC locations strategically housed within facilities of
                25,000 to 35,000 square feet, boasting ceilings at a minimum of 7 meters height.
                Each meticulously designed location showcases between 5 to 12 year-round indoor
                Padel courts, ensuring an unparalleled experience for enthusiasts of all levels.
              </p>
              <div className="mt-8">
                <Link
                  href="/contact"
                  className="inline-flex items-center rounded-full bg-[#ddba0a] px-8 py-4 font-bold text-[#fffaef] transition-colors hover:bg-[#c4a809]"
                >
                  Contact Us
                </Link>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-6">
              <Image
                className="w-full rounded-lg"
                src="/imgs/page/homepage3/img-design.png"
                alt="EPIC Venue"
                width={300}
                height={400}
                loading="lazy"
              />
              <Image
                className="mt-12 w-full rounded-lg"
                src="/imgs/page/homepage3/img-design2.png"
                alt="EPIC Venue Interior"
                width={300}
                height={400}
                loading="lazy"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Hybrid Clubs Section */}
      <section
        ref={(el: HTMLDivElement | null) => {
          sectionRefs.current[2] = el;
        }}
        className="py-20 md:py-32"
        id="3"
      >
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-6">
                <Image
                  className="w-full rounded-lg"
                  src="/imgs/page/homepage3/project.png"
                  alt="Hybrid Club"
                  width={400}
                  height={300}
                  loading="lazy"
                />
                <Image
                  className="w-full rounded-lg"
                  src="/imgs/page/homepage3/project2.png"
                  alt="Hybrid Club Interior"
                  width={400}
                  height={300}
                  loading="lazy"
                />
              </div>
              <div className="mt-12 space-y-6">
                <Image
                  className="w-full rounded-lg"
                  src="/imgs/page/homepage3/project3.png"
                  alt="Hybrid Club Exterior"
                  width={400}
                  height={300}
                  loading="lazy"
                />
                <Image
                  className="w-full rounded-lg"
                  src="/imgs/page/homepage3/project4.png"
                  alt="Hybrid Club View"
                  width={400}
                  height={300}
                  loading="lazy"
                />
              </div>
            </div>
            <div>
              <h1 className="mb-8 text-4xl font-semibold text-[#1c5534] md:text-5xl">
                Hybrid Clubs
              </h1>
              <p className="mt-16 text-lg text-gray-800 opacity-80 md:text-xl">
                Hybrid EPIC Clubs redefine the padel experience, creatively developed within parking
                lots and parks. Each location showcases between 4 to 6 courts, providing ample space
                for enthusiasts to indulge in the sport they love. In favorable weather seasons,
                enjoy the thrill of playing under the open sky on our courts, immersing yourself in
                the refreshing outdoor ambiance. As winter approaches, our cutting-edge roll-out
                canopies ensure that the Hybrid Clubs remain covered, allowing for uninterrupted
                gameplay and extending the enjoyment throughout the year.
              </p>
              <div className="mt-8">
                <Link
                  href="/contact"
                  className="inline-flex items-center rounded-full bg-[#ddba0a] px-8 py-4 font-bold text-[#fffaef] transition-colors hover:bg-[#c4a809]"
                >
                  Contact Us
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* EPIC Team Section */}
      <section
        ref={(el: HTMLDivElement | null) => {
          sectionRefs.current[3] = el;
        }}
        className="bg-background py-20 md:py-32"
        id="4"
      >
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
            <div>
              <h1 className="mb-8 text-4xl font-semibold text-[#1c5534] md:text-5xl">EPIC team</h1>
              <p className="mt-16 text-lg text-gray-800 opacity-80 md:text-xl">
                The EPIC team is ready. We know what it takes to build a successful business that
                provides an EPIC experience. We have recruited professionals from various
                backgrounds to help us execute our strategy. Our founders, co-founders and executive
                team include professionals with backgrounds in government relations, venture
                capital, real estate, technology, sports management, engineering, construction,
                media and communication.
              </p>
              <div className="mt-8">
                <Link
                  href="/contact"
                  className="inline-flex items-center rounded-full bg-[#ddba0a] px-8 py-4 font-bold text-[#fffaef] transition-colors hover:bg-[#c4a809]"
                >
                  Contact Us
                </Link>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-6">
              <Image
                className="mt-12 w-full rounded-lg"
                src="/imgs/page/homepage3/img-design5.png"
                alt="EPIC Team"
                width={300}
                height={400}
                loading="lazy"
              />
              <Image
                className="w-full rounded-lg"
                src="/imgs/page/homepage3/img-design4.png"
                alt="EPIC Team Members"
                width={300}
                height={400}
                loading="lazy"
              />
            </div>
          </div>
        </div>
      </section>
    </main>
  );
};

export default AboutPage;
