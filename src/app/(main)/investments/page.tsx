"use client";
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

const investments = [
  {
    title: "ULTRA CLUB",
    description:
      "ULTRA CLUB is Miami's premier sports and lifestyle destination, offering individuals, families, and friends the ultimate space to play, relax, dine, and socialize in comfort and style. In it's portfolio is the largest Padel facility in the United States, ULTRA CLUB features 15 state-of-the-art outdoor courts and 12 indoor courts, all equipped with innovative technology to enhance the playing experience. Whether you're a beginner or a seasoned Padel enthusiast, the club provides top-quality gear, personalized lessons, and a welcoming environment to fully immerse yourself in the sport. Ultra plans to scale its growth beyond Florida to Colorado, Nevada, California & New York.",
    link: "https://ultraclub.me",
    image: "/imgs/template/icons/ultra-club-min.png",
    mobileImage: "/imgs/template/icons/mobile-images/ultra-mobile-min.png",
    bgColor: "#000000",
    country: "/imgs/template/icons/usa-flag-min.png",
    industry: "Club Operator",
  },
  {
    title: "Pro Padel League",
    description:
      "The Pro Padel League is North America's first professional padel league, leading the charge of the world's fastest-growing sport. With franchise teams, major media attention, and a rapidly expanding fanbase, the PPL is transforming padel into a mainstream spectacle. It's fast-paced, high-energy, and built for the next generation of sports entertainment.",
    link: "https://propadelleague.com/",
    image: "/imgs/template/icons/ppl-min.png",
    mobileImage: "/imgs/template/icons/mobile-images/ppl-mobile-min.png",
    bgColor: "#000000",
    country: "/imgs/template/icons/usa-flag-min.png",
    industry: "League",
  },
  {
    title: "The New York Atlantics",
    description:
      "The New York Atlantics are the New York franchise of the Pro Padel League (PPL) - North America's professional circuit for padel.",
    link: "https://www.newyorkatlantics.com",
    image: "/imgs/template/icons/new-york-atlantics-min.png",
    mobileImage: "/imgs/template/icons/mobile-images/the-newyork-mobile-min.png",
    bgColor: "#274949",
    country: "/imgs/template/icons/usa-flag-min.png",
    industry: "Professional Team",
  },
  {
    title: "Hexagon Cup",
    description:
      "Starting with tournaments in Madrid, Spain annually & then hopefully expand to more territories around the world to bring a world class sporting and hospitality event.",
    link: "https://www.hexagoncup.com",
    image: "/imgs/template/icons/hexagon-cup-min.png",
    mobileImage: "/imgs/template/icons/mobile-images/hexagon-mobile-min.png",
    bgColor: "#370077",
    country: "/imgs/template/icons/spain-flag-min.png",
    industry: "Tournament",
  },
  {
    title: "Padel Haus",
    description:
      "Padel Haus is one of the leading padel operators in the U.S., having opened the first-ever padel club in NYC. It operates three locations in New York, one in Nashville, and will open new clubs in Atlanta and Denver in 2025—becoming the largest operator in the country with over 35 courts across four markets and a strong growth pipeline. Each location combines premium amenities—like upscale locker rooms, Juice Haus F&B, and elite coaching—with a focus on wellness, design, and community, delivering a uniquely elevated padel experience in the US",
    link: "https://www.padel.haus/",
    image: "/imgs/template/icons/padelhaus.jpeg",
    mobileImage: "/imgs/template/icons/padelhaus.jpeg",
    bgColor: "#370077",
    country: "/imgs/template/icons/usa-flag-min.png",
    industry: "Club Operator",
  },
  {
    title: "Padel 39",
    description:
      "Padel 39 is a US padel operator, based In Austin, Texas. Padel 39 Is set to open its first location near The Domain and Q2 Stadium in the summer of 2024 and aims to become the largest operator of courts in Texas.",
    link: "https://www.padel39.com",
    image: "/imgs/template/icons/padel-39-min.png",
    mobileImage: "/imgs/template/icons/mobile-images/padel-39-mobile-min.png",
    bgColor: "#ffffff",
    country: "/imgs/template/icons/usa-flag-min.png",
    industry: "Club Operator",
  },
  {
    title: "Clutch AI",
    description:
      "State of the art AI-powered cameras to track every shot and movement, and capture the best memories on the Padel courts.",
    link: "https://www.clutchapp.io",
    image: "/imgs/template/icons/clutch-ai-min.png",
    mobileImage: "/imgs/template/icons/mobile-images/cluth-mobile-min.png",
    bgColor: "#070707",
    country: "/imgs/template/icons/usa-flag-min.png",
    industry: "Smart Technology",
  },
  {
    title: "REDPADEL",
    description:
      "RedPADEL is a marketing, events and technology company exclusively dedicated to the wonderful sport of padel. At the core of RedPADEL is World Padel Rating (WPR), the official player rating system of the United States Padel Association.",
    link: "https://www.redpadel.com/",
    image: "/imgs/template/icons/redpadel-min.png",
    mobileImage: "/imgs/template/icons/mobile-images/red-padel-mobile-min.png",
    country: "/imgs/template/icons/usa-flag-min.png",
    bgColor: "#000",
    industry: "Rating System & Tournament Organizer",
  },
  {
    title: "The Padel Hub",
    description:
      "Padel Hub is the largest indoor padel operator in the UK. Creating a padel community both on and off the court, by bringing together premium indoor courts and luxurious club facilities. Padel Hub brings sport and social together so everyone can be part of the UK's newest and most exciting game. We're all about giving our players the ultimate experience, so we created the perfect space for you.",
    link: "https://www.padelhub.uk",
    image: "/imgs/template/icons/the-padel-hub-min.png",
    mobileImage: "/imgs/template/icons/mobile-images/the-padel-hub-mobile-min.png",
    bgColor: "rgb(25, 36, 63)",
    country: "/imgs/template/icons/england-flag-min.png",
    industry: "Club Operator",
  },
  {
    title: "Padel India",
    description:
      "Padel India is the leading padel brand in the world's most populous nation, India. The company is the dominant player in Hyderabad and Bangalore and plans to spread the sport and become the market leader across the country. In addition to operating clubs, Padel India owns India's pioneer padel app called Pi Play. Pi Play provides end-to-end discovery of the entire ecosystem, connecting players with clubs, coaches, competitions, and other players. Furthermore, Padel India is the exclusive distributor of Adidas Padel and pickleball related goods & MajorSet courts.",
    link: "https://www.padel-india.com",
    image: "/imgs/template/icons/padel-india-min.png",
    mobileImage: "/imgs/template/icons/mobile-images/padel-india-mboile-min.png",
    country: "/imgs/template/icons/india-flag-min.png",
    bgColor: "#000",
    industry: "Club Operator",
  },
  {
    title: "Two Two",
    description:
      "From Sweden, TWOTWO is revolutionizing the world of Padel with cutting edge gear and apparel. They offer Padel Rackets, Bags, Balls, & apparel.",
    link: "https://twotwo-official.com/en-en",
    image: "/imgs/template/icons/twotwo-min.png",
    mobileImage: "/imgs/template/icons/mobile-images/two-two-mobile-min.png",
    country: "/imgs/template/icons/sweden-flag-min.png",
    bgColor: "#defd01",
    industry: "Apparel Brand",
  },
];

const InvestmentsPage = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!sectionRef.current) return;

    const ctx = gsap.context(() => {
      // Banner animation
      gsap.from(".banner-content", {
        y: 100,
        opacity: 0,
        duration: 1,
        ease: "power3.out",
      });

      // Investment items animation
      const items = gsap.utils.toArray<HTMLElement>(".investment-item");
      items.forEach((item, i) => {
        // Initial fade in animation
        gsap.from(item, {
          scrollTrigger: {
            trigger: item,
            start: "top bottom-=100",
            toggleActions: "play none none reverse",
          },
          y: 50,
          opacity: 0,
          duration: 0.8,
          delay: i * 0.2,
          ease: "power2.out",
        });

        // Scroll-based opacity animation
        ScrollTrigger.create({
          trigger: item,
          start: "top bottom",
          end: "center center",
          onEnter: () => gsap.to(item, { opacity: 1, duration: 0.3 }),
          onLeaveBack: () => gsap.to(item, { opacity: 0.5, duration: 0.3 }),
          onLeave: () => gsap.to(item, { opacity: 0.5, duration: 0.3 }),
          onEnterBack: () => gsap.to(item, { opacity: 1, duration: 0.3 }),
        });
      });

      // Parallax hover effect
      const calcValue = (
        value: number,
        inputMax: number,
        inputMin: number,
        outputMax: number,
        outputMin: number
      ) => {
        const percent = (value - inputMin) / (inputMax - inputMin);
        const output = percent * (outputMax - outputMin) + outputMin;
        return output;
      };

      const itemWraps = document.querySelectorAll(".investment-item");
      itemWraps.forEach((itemWrap) => {
        itemWrap.addEventListener("mousemove", (e: Event) => {
          const mouseX = (e as MouseEvent).clientX;
          const mouseY = (e as MouseEvent).clientY;
          const rect = itemWrap.getBoundingClientRect();
          const offsetX = rect.left + rect.width / 2;
          const offsetY = rect.top + rect.height / 2;
          const deltaX = mouseX - offsetX;
          const deltaY = mouseY - offsetY;
          const percentX = deltaX / (itemWrap.clientWidth / 2);
          const percentY = deltaY / (itemWrap.clientHeight / 2);

          gsap.to(itemWrap, {
            duration: 0.5,
            x: calcValue(percentX, 1, -1, 20, -20),
            y: calcValue(percentY, 1, -1, 20, -20),
            rotationX: calcValue(percentY, 1, -1, 5, -5),
            rotationY: calcValue(percentX, 1, -1, -5, 5),
            ease: "power1.out",
          });
        });

        itemWrap.addEventListener("mouseleave", () => {
          gsap.to(itemWrap, {
            duration: 0.5,
            x: 0,
            y: 0,
            rotationX: 0,
            rotationY: 0,
            ease: "power1.out",
          });
        });
      });
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="border-primary h-32 w-32 animate-spin rounded-full border-t-2 border-b-2"></div>
      </div>
    );
  }

  return (
    <div className="bg-background min-h-screen py-20 md:py-32">
      <div className="px-4">
        <div className="mx-auto max-w-[847px]">
          <div className="banner-content translate-y-5">
            <div className="flex items-center gap-3">
              <Image
                src="/imgs/template/logo-green-small.svg"
                alt="neuron"
                width={65}
                height={65}
                className="mb-1"
                priority
              />
              <h2 className="from-primary bg-gradient-to-r to-green-800 bg-clip-text text-3xl font-bold tracking-tight text-transparent md:text-6xl">
                Epic Investments
              </h2>
            </div>
            <p className="font-helvetica pt-5 text-xl font-medium text-gray-900">
              Investing in padel startups driving innovation in the world's fastest-growing
              sport—focusing on U.S. and emerging market-based operators, professional leagues,
              tournaments, technology solutions, and performance apparel.
            </p>
          </div>
        </div>
      </div>

      <section className="px-4 py-12">
        <div className="mx-auto max-w-[847px]">
          <div ref={containerRef} className="flex flex-col gap-24">
            {investments.map((item, index) => (
              <div
                key={index}
                className="investment-item translate-y-5 transition-all duration-300 ease-in-out hover:-translate-y-1"
              >
                <h3 className="text-primary mb-4 text-3xl font-semibold tracking-tight md:text-4xl">
                  {item.title}
                </h3>
                <div className="mb-6 h-[3px] bg-black"></div>
                <div
                  className="relative mb-6 h-[200px] w-full overflow-hidden rounded-lg transition-transform duration-300 ease-in-out hover:scale-[1.02]"
                  style={{ backgroundColor: item.bgColor }}
                >
                  <Image
                    src={item.image}
                    alt={item.title}
                    fill
                    className="object-contain p-4"
                    priority={index < 2}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "/imgs/placeholder.svg";
                    }}
                  />
                </div>
                <p className="mb-6 font-sans text-xl font-medium text-gray-900">
                  {item.description}
                </p>
                <div className="mb-6 h-px bg-black"></div>
                <div className="flex items-center justify-between">
                  <div className="flex gap-8">
                    <div>
                      <h3 className="font-helvetica mb-2 text-sm font-semibold text-gray-600">
                        Country
                      </h3>
                      <div className="relative h-8 w-8">
                        <Image
                          src={item.country}
                          alt="country flag"
                          fill
                          className="object-contain"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = "/imgs/placeholder.svg";
                          }}
                        />
                      </div>
                    </div>
                    <div>
                      <h3 className="font-helvetica mb-2 text-sm font-semibold text-gray-600">
                        Industry
                      </h3>
                      <p className="text-gray-900">{item.industry}</p>
                    </div>
                  </div>
                  <Link
                    href={item.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:text-primary/90 font-medium transition-colors hover:scale-[102%]"
                  >
                    Visit Website →
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default InvestmentsPage;
