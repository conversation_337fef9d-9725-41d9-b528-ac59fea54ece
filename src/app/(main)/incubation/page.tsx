"use client";

import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Link from "next/link";
import { useEffect, useRef } from "react";

gsap.registerPlugin(ScrollTrigger);

const IncubationPage = () => {
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!sectionRef.current) return;

    const ctx = gsap.context(() => {
      // Banner animation
      gsap.from(".banner-content", {
        y: 100,
        opacity: 0,
        duration: 1,
        ease: "power3.out",
      });

      // Project items animation
      const items = gsap.utils.toArray<HTMLElement>(".project-item");
      items.forEach((item, i) => {
        gsap.from(item, {
          scrollTrigger: {
            trigger: item,
            start: "top bottom-=100",
            toggleActions: "play none none reverse",
          },
          y: 50,
          opacity: 0,
          duration: 0.8,
          delay: i * 0.2,
          ease: "power2.out",
        });
      });

      // Future projects animation
      gsap.from(".future-projects-title", {
        scrollTrigger: {
          trigger: ".future-projects-title",
          start: "top bottom-=100",
          toggleActions: "play none none reverse",
        },
        x: -100,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
      });

      const futureProjects = gsap.utils.toArray<HTMLElement>(".future-project-card");
      futureProjects.forEach((card, i) => {
        gsap.from(card, {
          scrollTrigger: {
            trigger: card,
            start: "top bottom-=100",
            toggleActions: "play none none reverse",
          },
          y: 50,
          opacity: 0,
          duration: 0.8,
          delay: i * 0.2,
          ease: "power2.out",
        });
      });
    });

    return () => ctx.revert();
  }, []);

  return (
    <div className="bg-background min-h-screen" ref={sectionRef}>
      {/* Banner Section */}
      <section className="section-banner-bloglist2 relative py-20 md:py-32">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h2 className="banner-content text-primary text-4xl font-bold md:text-6xl">
              Incubation
            </h2>
            <p className="font-helvetica mt-12 text-xl font-medium text-gray-900 md:text-2xl">
              America is the innovation capital country of the world. We at EPIC strive to become
              the most innovative padel company in North America. As veteran venture capitalists we
              understand and appreciate the power of technology and its impact on our lives.
            </p>
          </div>
        </div>
      </section>

      {/* Volee Project Section */}
      <section className="">
        <div className="container mx-auto px-4">
          <div className="grid items-center gap-12 lg:grid-cols-2">
            <div className="project-item">
              <h2 className="text-primary mb-8 text-4xl font-bold md:text-5xl">Volee</h2>
              <p className="mb-12 text-xl text-gray-900">
                Our first incubation project is "Volee" a full stack padel app that allows EPIC
                padel and future clients to white label its services. Volee's services includes
              </p>
              <div className="mt-8">
                <Link
                  href="/contact"
                  className="bg-secondary text-primary inline-block rounded-full px-8 py-4 text-lg font-semibold transition-all duration-300 hover:scale-105 hover:bg-[#ddba12]"
                >
                  Contact Us
                </Link>
              </div>
            </div>
            <div className="project-item space-y-6">
              <div className="font-helvetica rounded-full bg-[#fcf5e1] p-8 text-xl font-medium shadow-lg">
                <p className="text-xl text-gray-900">Profile and content creation by members</p>
              </div>
              <div className="font-helvetica ml-12 rounded-full bg-[#fcf5e1] p-8 text-xl font-medium shadow-lg">
                <p className="text-xl text-gray-900">Court booking</p>
              </div>
              <div className="font-helvetica rounded-full bg-[#fcf5e1] p-8 text-xl font-medium shadow-lg">
                <p className="text-xl text-gray-900">Booking training sessions</p>
              </div>
              <div className="font-helvetica ml-12 rounded-full bg-[#fcf5e1] p-8 text-xl font-medium shadow-lg">
                <p className="text-xl text-gray-900">Instant video integration</p>
              </div>
              <div className="font-helvetica rounded-full bg-[#fcf5e1] p-8 text-xl font-medium shadow-lg">
                <p className="text-xl text-gray-900">
                  NFT collectables linked with several utilities in the world of padel
                </p>
              </div>
              <div className="font-helvetica ml-12 rounded-full bg-[#fcf5e1] p-8 text-xl font-medium shadow-lg">
                <p className="text-xl text-gray-900">
                  Un-locking crypto-rewards by completing challenges
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Future Projects Section */}
      <section className="px-4 py-20 md:py-32">
        <div className="container mx-auto">
          <h3 className="future-projects-title text-outline mb-12 text-right text-4xl font-bold text-gray-900 md:text-8xl">
            Future Incubation Projects
          </h3>
          <div className="grid gap-8 rounded-[80px] bg-[#fcf5e1] px-12 py-24 md:grid-cols-2 lg:grid-cols-3">
            <div className="future-project-card rounded-lg bg-transparent p-8 transition-all duration-300 hover:scale-105">
              <h3 className="text-primary mb-2 text-4xl font-bold">Epicademy</h3>
              <p className="text-primary mb-4">Academy</p>
              <p className="text-gray-700">
                Supply of world class coaching sessions and coaching certification for those who
                want to become coaches. Launching 2026.
              </p>
            </div>
            <div className="future-project-card rounded-lg bg-transparent p-8 transition-all duration-300 hover:scale-105">
              <h3 className="text-primary mb-2 text-4xl font-bold">Epiholic</h3>
              <p className="text-primary mb-4">E Commerce</p>
              <p className="text-gray-700">
                Supply of world class coaching sessions and coaching certification for those who
                want to become coaches. Launching 2026.
              </p>
            </div>
            <div className="future-project-card rounded-lg bg-transparent p-8 transition-all duration-300 hover:scale-105">
              <h3 className="text-primary mb-2 text-4xl font-bold">EpiCourts</h3>
              <p className="text-primary mb-4">Courts</p>
              <p className="text-gray-700">
                We are currently in talks with partners in Egypt to manufacture a world class
                high-tech court powered by AI and sensors that we anticipate rolling out in 2026
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default IncubationPage;
