"use client";

import LocationSection from "@/components/locations/location-section";
import { notFound, useParams } from "next/navigation";

type LocationSlug = "charlotte" | "virginia" | "utah";

export interface Location {
  slug: LocationSlug;
  name: string;
  title: string;
  description: string;
  mapUrl: string;
  comingSoon?: boolean;
  bannerImage: string;
}

const locations: Location[] = [
  {
    slug: "charlotte",
    name: "Charlotte, NC",
    title: "Epic Padel is Coming to Prosperity Athletic Club!",
    description: `<p className="">
                  <strong>Epic Padel Is Coming to Prosperity Athletic Club!</strong>
                </p>
                <p>
                  We&apos;re thrilled to announce the opening of <strong>Epic Padel</strong> at 
                  <strong>Prosperity Athletic Club</strong> in Charlotte, NC! Get ready for
                  world-class padel action with <strong>5 brand-new outdoor courts</strong> set to
                  open this summer.
                </p>
                <p>
                  Whether you're new to the sport or a seasoned player, Epic Padel offers a premium
                  playing experience, a vibrant community, and easy court access all in one of the
                  city's most exciting athletic environments.
                </p>
                <p>
                  <strong>🎾 Memberships are now on sale!</strong>
                  <p>
                    Be among the first to join and enjoy exclusive perks, unlimited instant court
                    bookings, and special access to launch events.
                  </p>
                </p>
                <p>
                  <strong>Join the Epic Padel community today.</strong>
                </p>
                <a
                  href="/membership"
                  className="join-btn hover:bg-primary/90 font-helvetica inline-block rounded-full bg-primary px-6 py-3 font-bold text-white transition-all"
                >
                  Join Now
                </a>
                `,
    mapUrl: "https://snazzymaps.com/embed/711610",
    comingSoon: true,
    bannerImage: "/imgs/page/portfolio-detail/charlotte.png",
  },
  {
    slug: "virginia",
    name: "Tyson's Corner, VA",
    title: "Tyson's Corner, VA",
    description: `<p>Our team is working hard to open our new outdoor facility! Stay tuned for more updates, and get ready for an incredible experience. We can't wait to welcome you!</p>`,
    mapUrl: "https://snazzymaps.com/embed/609240",
    bannerImage: "/imgs/page/portfolio-detail/banner.png",
  },
  {
    slug: "utah",
    name: "Salt Lake City, UT",
    title: "Salt Lake City, UT",
    description: `<p>Our team is working hard to open our new outdoor facility! Stay tuned for more updates, and get ready for an incredible experience. We can't wait to welcome you!</p>`,
    mapUrl: "https://snazzymaps.com/embed/707428",
    bannerImage: "/imgs/page/portfolio-detail/banner-utah.jpg",
  },
];

export default function LocationDetailPage() {
  const slug = useParams().slug;
  const location = locations.find((loc) => loc.slug === slug);

  if (!location) {
    return notFound();
  }

  return (
    <main className="min-h-screen">
      <LocationSection location={location} />

      {/* App Section */}
      {/* <DownloadAppSection /> */}
    </main>
  );
}
