"use client";

import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useRef } from "react";

const StorePage = () => {
  const router = useRouter();
  const scrollMoveUpRef = useRef(null);
  const parallaxImageRef = useRef(null);
  const headingRef = useRef(null);

  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger);

    // Heading animation
    gsap.fromTo(
      headingRef.current,
      {
        y: 50,
        opacity: 0,
      },
      {
        y: 0,
        opacity: 1,
        duration: 1.2,
        ease: "power3.out",
      }
    );

    // Scroll move up animation for left content
    gsap.fromTo(
      scrollMoveUpRef.current,
      {
        y: 100,
        opacity: 0,
      },
      {
        y: 0,
        opacity: 1,
        duration: 1.5,
        ease: "power3.out",
        scrollTrigger: {
          trigger: scrollMoveUpRef.current,
          start: "top bottom-=100",
          end: "top center",
          toggleActions: "play none none reverse",
          markers: false,
        },
      }
    );

    // Parallax image animation with enhanced effect
    gsap.to(parallaxImageRef.current, {
      y: 100,
      scale: 1.05,
      duration: 2,
      ease: "none",
      scrollTrigger: {
        trigger: parallaxImageRef.current,
        start: "top bottom",
        end: "bottom top",
        scrub: 1.5,
        markers: false,
      },
    });

    // Cleanup function
    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, []);

  return (
    <main className="bg-background min-h-screen pt-20 md:pt-32">
      <section className="w-full">
        {/* Banner Section */}
        <section className="py-8">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h1
                ref={headingRef}
                className="bg-primary bg-clip-text text-6xl font-bold text-transparent"
              >
                Store
              </h1>
            </div>
          </div>
        </section>

        {/* Coming Soon Section */}
        <section className="pt-10 pb-20">
          <div className="container mx-auto px-4">
            <div className="flex flex-col items-center gap-12 lg:flex-row">
              {/* Left Content */}
              <div className="lg:w-1/3">
                <p className="font-helvetica text-2xl text-gray-900 opacity-90">
                  We are preparing something amazing and exciting for you. Special surprise for our
                  subscribers only.
                </p>
                <div className="mt-12">
                  <button
                    className="text-primary z-1 inline-block cursor-pointer rounded-full bg-[#ddba0a] px-8 py-4 text-lg font-semibold shadow-lg transition-all duration-300 ease-out hover:scale-105 hover:bg-[#ddba12] hover:shadow-xl sm:px-10 sm:py-4 sm:text-xl md:px-12 md:py-5 md:text-2xl"
                    onClick={() => {
                      router.push("/contact");
                    }}
                  >
                    Contact
                  </button>
                </div>
              </div>

              {/* Right Content */}
              <div className="mb-16 h-[400px] w-full lg:h-[600px] lg:w-2/3">
                <div className="h-full w-full">
                  <div className="h-full w-full">
                    <Image
                      ref={parallaxImageRef}
                      src="/imgs/page/commingsoon/banner.png"
                      alt="Coming Soon"
                      className="h-[400px] w-full rounded-lg object-cover lg:h-[600px]"
                      width={400}
                      height={600}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </section>
    </main>
  );
};

export default StorePage;
