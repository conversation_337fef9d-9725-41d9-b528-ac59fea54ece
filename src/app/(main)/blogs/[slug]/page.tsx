import React from "react";
import Image from "next/image";
import Link from "next/link";
import { notFound } from "next/navigation";
import { formatDate } from "@/libs/utils";
import { endpoints } from "@/api/axios";
import BlogCard from "@/components/blogs/blog-card";

// TypeScript interfaces for the blog data structure
interface BlogItem {
  id: number;
  slug: string;
  title: string;
  description: string;
  content: string;
  cover_image?: string;
  banner_image?: string;
  cover_image_alt?: string;
  created_at: string;
  meta_keywords?: string;
  meta_description?: string;
  relativesBlogs?: BlogItem[];
}

interface BlogApiResponse {
  data: BlogItem;
}

// Generate metadata for the page
export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }) {
  try {
    const { slug } = await params;
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL!}${endpoints.public.blog.content}/${slug}`,
      { next: { revalidate: 3600 } } // Revalidate every hour
    );

    if (!response.ok) {
      return {
        title: "Blog Not Found | Epic Padel",
        description: "The requested blog post could not be found.",
      };
    }

    const data: BlogApiResponse = await response.json();
    // console.log("data", data);
    const blog = data.data;

    return {
      title: `${blog.title} | Epic Padel`,
      description: blog.meta_description || blog.description,
      openGraph: {
        title: `${blog.title} | Epic Padel`,
        description: blog.meta_description || blog.description,
        images: [blog.cover_image || blog.banner_image || "/imgs/template/logo.svg"],
        type: "article",
      },
      twitter: {
        card: "summary_large_image",
        title: `${blog.title} | Epic Padel`,
        description: blog.meta_description || blog.description,
        images: [blog.cover_image || blog.banner_image || "/imgs/template/logo.svg"],
      },
    };
  } catch (error) {
    return {
      title: "Blog Not Found | Epic Padel",
      description: "The requested blog post could not be found.",
    };
  }
}

async function getBlogContent(slug: string) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL!}/public/blog/content/${slug}`,
      { next: { revalidate: 3600 } } // Revalidate every hour
    );

    if (!response.ok) {
      return null;
    }

    const data: BlogApiResponse = await response.json();
    return data.data;
  } catch (error) {
    console.error("Error fetching blog content:", error);
    return null;
  }
}

const BlogDetailPage = async ({ params }: { params: Promise<{ slug: string }> }) => {
  const { slug } = await params;
  //   console.log("slug", slug);
  const blog = await getBlogContent(slug);

  if (!blog) {
    notFound();
  }

  return (
    <div className="bg-background container mx-auto min-h-screen px-4 pt-24 pb-16 md:pt-32">
      <div id="blog-detail-container" className="mx-auto max-w-4xl">
        <h1 id="blog-title" className="mb-4 text-4xl font-medium text-black">
          {blog.title}
        </h1>

        <div className="h-80 w-full rounded-2xl">
          <Image
            src={blog.cover_image || blog.banner_image || "/imgs/placeholder.svg"}
            alt={blog.cover_image_alt || blog.title}
            width={1200}
            height={600}
            className="h-full w-full rounded-2xl object-cover"
            loading="lazy"
          />
        </div>

        <div className="my-4 flex w-full justify-end">
          <div className="inline-flex items-center justify-start rounded-2xl border border-[#fedf89] bg-[#fffaeb] px-3 py-[1px]">
            <p className="text-center text-[10px] font-normal break-words text-[#b54708]">
              {formatDate(blog.created_at)}
            </p>
          </div>
        </div>

        <div
          className="blog-content max-w-4xl"
          dangerouslySetInnerHTML={{ __html: blog.content }}
        />

        {blog.relativesBlogs && blog.relativesBlogs.length > 0 && (
          <div className="related-blogs-section mt-16">
            <h3 className="heading-3 font-helvetica mb-8 text-2xl font-medium text-black">
              Related Blogs
            </h3>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              {blog.relativesBlogs.map((relatedBlog) => (
                <BlogCard key={relatedBlog.id} blog={relatedBlog} />
              ))}
            </div>
          </div>
        )}

        <div className="mt-10">
          <Link
            href="/blogs"
            className="inline-flex items-center rounded-full bg-[#1c5534] px-6 py-3 text-sm font-medium text-white hover:bg-[#0c4825]"
          >
            Back to Blogs
          </Link>
        </div>
      </div>
    </div>
  );
};

export default BlogDetailPage;
