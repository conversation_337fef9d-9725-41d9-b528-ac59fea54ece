"use server";

import { SESClient, SendEmailCommand } from "@aws-sdk/client-ses";
import { z } from "zod";

// Contact form validation schema
const contactFormSchema = z.object({
  "full-name": z.string().min(2, "Name must be at least 2 characters"),
  "phone-number": z.string().optional(),
  "email-address": z.string().email("Invalid email address"),
  subject: z.string().min(3, "Subject must be at least 3 characters"),
  message: z.string().min(10, "Message must be at least 10 characters"),
});

// Initialize SES client
const sesClient = new SESClient({
  region: process.env.AWS_REGION! || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID! || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY! || "",
  },
});

const generateEmailTemplate = (data: z.infer<typeof contactFormSchema>) => `
<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f9;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .container {
            width: 80%;
            margin: auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            background-color: #1c5534;
            color: white;
            padding: 10px;
            text-align: center;
            border-radius: 10px 10px 0 0;
        }
        .content {
            padding: 20px;
        }
        .footer {
            text-align: center;
            padding: 10px;
            background-color: #1c5534;
            color: white;
            border-radius: 0 0 10px 10px;
        }
        .footer a {
            color: #fff;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>Contact Us Form Submission</h1>
        </div>
        <div class='content'>
            <p><strong>Full Name:</strong> ${data["full-name"]}</p>
            <p><strong>Email:</strong> ${data["email-address"]}</p>
            <p><strong>Subject:</strong> ${data.subject}</p>
            <p><strong>Message:</strong></p>
            <p>${data.message}</p>
            ${data["phone-number"] ? `<p><strong>Phone:</strong> ${data["phone-number"]}</p>` : ""}
        </div>
        <div class='footer'>
            <p>Thank you for contacting us!</p>
            <p><a href='https://epic-padel.com/'>Visit our website</a></p>
        </div>
    </div>
</body>
</html>
`;

export async function sendContactEmail(formData: FormData) {
  try {
    // Parse and validate form data
    const validatedData = contactFormSchema.parse({
      "full-name": formData.get("full-name"),
      "phone-number": formData.get("phone-number"),
      "email-address": formData.get("email-address"),
      subject: formData.get("subject"),
      message: formData.get("message"),
    });

    // Prepare email content
    const emailParams = {
      Source: process.env.AWS_SES_FROM_EMAIL || "<EMAIL>",
      Destination: {
        ToAddresses: [process.env.AWS_SES_TO_EMAIL || "<EMAIL>"],
      },
      Message: {
        Subject: {
          Data: "Epic Padel - Contact Us Form",
        },
        Body: {
          Html: {
            Data: generateEmailTemplate(validatedData),
          },
        },
      },
    };

    // Send email using AWS SES
    await sesClient.send(new SendEmailCommand(emailParams));

    return { success: true, message: "Message sent successfully!" };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, message: error.errors[0].message };
    }
    console.error("Error sending email:", error);
    return { success: false, message: "Failed to send message. Please try again later." };
  }
}
