"use client";
import BookingSection from "@/components/booking/booking-section";
import SportSelector from "@/components/booking/sport-selector";
import { useBookingStore } from "@/store/booking-store";
import React, { Suspense } from "react";

const BookingPage = () => {
  const sportItems = [
    {
      name: "Padel",
      image: "/imgs/booking/padel.png",
      comingSoon: false,
    },
    {
      name: "Pickleball",
      image: "/imgs/booking/pickleball.png",
      comingSoon: true,
    },
    {
      name: "Tennis",
      image: "/imgs/booking/tennis.png",
      comingSoon: true,
    },
  ];

  const { selectedSport } = useBookingStore();

  return (
    <div className="h-full w-full">
      <div
        className="relative flex h-[260px] flex-col items-center justify-end px-4 md:h-[400px]"
        style={{
          backgroundImage: `url(${sportItems.find((item) => item.name === selectedSport)?.image})`,
          backgroundRepeat: "no-repeat",
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundBlendMode: "luminosity",
        }}
      >
        <div className="absolute top-0 left-0 h-full w-full bg-black/60" />
        <div className="z-10 w-full flex-col items-center justify-start">
          <h1 className="font-helvetica justify-center text-center text-2xl font-bold text-white md:text-4xl">
            Booking Options
          </h1>
          <p className="font-helvetica justify-center self-stretch text-center font-normal text-white">
            Choose how you play. Book the experience that fits you.
          </p>
        </div>
        <SportSelector />
      </div>

      <Suspense>
        <div className="relative mb-20 w-full pt-10">
          <BookingSection locationSlug="charlotte" isFromBookingPage={true} />
        </div>
      </Suspense>
    </div>
  );
};

export default BookingPage;
