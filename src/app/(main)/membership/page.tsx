"use client";
import DownloadAppSection from "@/components/home/<USER>";
import MembershipSection from "@/components/home/<USER>";
import TestimonialSection from "@/components/home/<USER>";
import MembershipStep from "@/components/subscription/membership-step";
import { useSubscriptionStore } from "@/store/subscription-store";
import { useRouter } from "next/navigation";
import { use } from "react";

const MembershipPage = () => {
  const router = useRouter();
  const { currentStep, selectedPlan, setCurrentStep, setSelectedPlan } = useSubscriptionStore();

  const handleNext = () => {
    router.push("/subscription");
    console.log("handleNext");
    console.log("currentStep", currentStep);
    setCurrentStep(2);
  };

  return (
    <div className="bg-background min-h-screen pt-20 md:pt-32">
      {/* <MembershipSection /> */}
      <div className="font-helvetica p-4">
        <MembershipStep
          selectedPlan={selectedPlan}
          onSelectPlan={setSelectedPlan}
          onNext={handleNext}
        />
      </div>
      <div className="bg-background w-full py-16">
        <MembershipSection />
      </div>

      <div className="bg-background w-full py-16">
        <TestimonialSection />
      </div>
      <div className="bg-background w-full py-32">
        <DownloadAppSection />
      </div>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "OfferCatalog",
            name: "Epic Padel Membership Plans",
            url: "https://epic-padel.com/membership",
            provider: {
              "@type": "SportsActivityLocation",
              name: "Epic Padel",
              url: "https://epic-padel.com/",
              address: {
                "@type": "PostalAddress",
                streetAddress: "4112 Cherry Hill Rd",
                addressLocality: "Arlington",
                addressRegion: "VA",
                postalCode: "22207",
                addressCountry: "US",
              },
            },
            itemListElement: [
              {
                "@type": "Offer",
                name: "Basic Membership",
                description: "Access to courts during off-peak hours.",
                price: "49.99",
                priceCurrency: "USD",
                availability: "https://schema.org/InStock",
              },
              {
                "@type": "Offer",
                name: "Premium Membership",
                description: "Full access to all courts and premium booking hours.",
                price: "99.99",
                priceCurrency: "USD",
                availability: "https://schema.org/InStock",
              },
              {
                "@type": "Offer",
                name: "Family Membership",
                description: "Includes access for up to 4 family members.",
                price: "149.99",
                priceCurrency: "USD",
                availability: "https://schema.org/InStock",
              },
            ],
          }),
        }}
      />
    </div>
  );
};

export default MembershipPage;
