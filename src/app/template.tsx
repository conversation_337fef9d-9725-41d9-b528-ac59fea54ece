"use client";

import gsap from "gsap";
import Image from "next/image";
import { ReactNode, useEffect, useRef } from "react";
import { usePathname } from "next/navigation";

export default function Template({ children }: { children: ReactNode }) {
  const pathname = usePathname();
  const loaderRef = useRef<HTMLDivElement>(null);
  const hasAnimated = useRef(false);

  useEffect(() => {
    // Reset the animation state for new route
    hasAnimated.current = false;

    // Reset loader visibility and bars height
    if (loaderRef.current) {
      gsap.set(loaderRef.current, { display: "flex", opacity: 1 });
      gsap.set(".bar", { height: "105vh" });
    }

    // Create the animation timeline
    const tl = gsap.timeline();

    // Animate the bars
    tl.to(".bar", {
      duration: 0.5,
      height: 0,
      stagger: {
        amount: 0.3,
      },
      ease: "power4.inOut",
    });

    // Fade out the loader
    setTimeout(() => {
      gsap.to(".page-loader", {
        duration: 1,
        opacity: 0,
        onComplete: () => {
          const loader = document.querySelector(".page-loader") as HTMLElement;
          if (loader) {
            loader.style.display = "none";
          }
          hasAnimated.current = true;
        },
      });
    }, 1000);

    // Cleanup function
    return () => {
      // Kill any running animations
      gsap.killTweensOf(".bar");
      gsap.killTweensOf(".page-loader");
    };
  }, [pathname]); // Re-run animation when pathname changes

  return (
    <>
      <div ref={loaderRef} className="page-loader">
        <div className="page-loader-logo hide-animation">
          <Image
            alt="epic logo"
            title="epic logo"
            src="/imgs/template/logo.svg"
            width={200}
            height={100}
            className="h-auto w-32 md:w-48"
            loading="lazy"
          />
        </div>
        <div className="bar"></div>
        <div className="bar"></div>
        <div className="bar"></div>
        <div className="bar"></div>
        <div className="bar"></div>
        <div className="bar"></div>
        <div className="bar"></div>
        <div className="bar"></div>
        <div className="bar"></div>
        <div className="bar"></div>
      </div>
      {children}
    </>
  );
}
