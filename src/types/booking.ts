export type SportType = "Padel" | "Pickleball" | "Tennis";
export type BookingType = "court" | "lesson" | "program" | "instructor";
export type ProgramType = "FULL" | "PER_SESSION";
export type BookingStatus = "confirmed" | "pending" | "cancelled" | "completed";

export interface BookingFormData {
    court: string;
    date: string;
    time: string;
    duration: string;
    //   players: number;
    //   clutchAi: boolean;
    //   package: string;
}

export interface Court {
    id: number;
    name?: string;
    [key: string]: any; // Allow for additional properties
}

// Interface for court booking items from API
export interface CourtBookingItem {
    id: number;
    booking_id?: string;
    court_name?: string;
    sub_court_name?: string;
    date: string;
    start_time: string;
    end_time: string;
    status?: BookingStatus;
    booking_type?: string;
    price?: number;
    location?: {
        name?: string;
        address?: string;
    };
    created_at?: string;
    updated_at?: string;
}