import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

/**
 * Checkout Store with Persistence
 *
 * This store persists checkout data to localStorage to maintain state across page refreshes.
 * The persisted data includes:
 * - checkoutData: The current checkout information (booking_id, payment method, etc.)
 * - selectedCourtAttributes: Selected court attributes and their quantities
 *
 * UI state (isProcessing, isCompleted, error) is not persisted and will reset on page refresh.
 */

// Payment method types based on the API structure
export type PaymentMethodType = "APPLE_PAY" | "CARD" | "WALLET";

// Payment method interface
export interface PaymentMethod {
    id: string;
    brand: string;
    last_number: string;
    is_default: boolean;
}

// Court attribute interface
export interface CourtAttribute {
    id: number;
    icon: string | null;
    amount: string;
    min_value: number;
    max_value: number | null;
    discounted: boolean;
    discount_amount: number;
    after_discount: string;
    icon_picture: string | null;
    court_attribute_id: number;
    court_attribute: {
        name: string;
        iconPicture: {
            path: string;
            alt: string;
            title: string;
        }
    }
}

// Selected court attribute interface
export interface SelectedCourtAttribute {
    court_attribute_id: number;
    value: number;
}

// Checkout data interface matching the required structure
export interface CheckoutData {
    booking_id: number;
    payment_method: PaymentMethodType;
    payment_method_id: number;
    use_wallet: 0 | 1;
    court_attributes?: SelectedCourtAttribute[];
}

// Checkout state interface
interface CheckoutState {
    // Current checkout data
    checkoutData: CheckoutData | null;
    totalAmount: number;

    // Court attributes selection
    selectedCourtAttributes: SelectedCourtAttribute[];
    lastTimetoPay: string | null;

    // UI state
    isProcessing: boolean;
    isCompleted: boolean;
    error: string | null;

    // Actions
    setCheckoutData: (data: CheckoutData) => void;
    updatePaymentMethod: (paymentMethod: PaymentMethodType, paymentMethodId: number) => void;
    updateWalletUsage: (useWallet: boolean) => void;
    updateCourtAttribute: (courtAttributeId: number, value: number) => void;
    removeCourtAttribute: (courtAttributeId: number) => void;
    clearCourtAttributes: () => void;
    setProcessing: (processing: boolean) => void;
    setCompleted: (completed: boolean) => void;
    setError: (error: string | null) => void;
    clearCheckout: () => void;
    setTotalAmount: (amount: number) => void;
    setLastTimetoPay: (time: string | null) => void;



    // Helper methods
    getCheckoutPayload: () => CheckoutData | null;
}

export const useCheckoutStore = create<CheckoutState>()(
    persist(
        (set, get) => ({
            // Initial state
            checkoutData: null,
            selectedCourtAttributes: [],
            isProcessing: false,
            isCompleted: false,
            error: null,
            totalAmount: 0,
            lastTimetoPay: null,




            // Actions
            setCheckoutData: (data: CheckoutData) => {
                set({
                    checkoutData: data,
                    error: null
                });
            },

            updatePaymentMethod: (paymentMethod: PaymentMethodType, paymentMethodId: number) => {
                const currentData = get().checkoutData;
                // console.log("currentData", currentData, paymentMethod, paymentMethodId);
                if (currentData) {
                    set({
                        checkoutData: {
                            ...currentData,
                            payment_method: paymentMethod,
                            payment_method_id: paymentMethodId,
                        },
                        error: null
                    });
                } else {
                    set({
                        checkoutData: {
                            booking_id: 0,
                            payment_method: paymentMethod,
                            payment_method_id: paymentMethodId,
                            use_wallet: 0,
                        },
                        error: null
                    });
                }
            },

            updateWalletUsage: (useWallet: boolean) => {
                const currentData = get().checkoutData;
                if (currentData) {
                    set({
                        checkoutData: {
                            ...currentData,
                            use_wallet: useWallet ? 1 : 0
                        },
                        error: null
                    });
                }
            },

            updateCourtAttribute: (courtAttributeId: number, value: number) => {
                const currentAttributes = get().selectedCourtAttributes;
                const existingIndex = currentAttributes.findIndex(attr => attr.court_attribute_id === courtAttributeId);

                let updatedAttributes: SelectedCourtAttribute[];

                if (value === 0) {
                    // Remove attribute if value is 0
                    updatedAttributes = currentAttributes.filter(attr => attr.court_attribute_id !== courtAttributeId);
                } else if (existingIndex >= 0) {
                    // Update existing attribute
                    updatedAttributes = [...currentAttributes];
                    updatedAttributes[existingIndex] = { court_attribute_id: courtAttributeId, value };
                } else {
                    // Add new attribute
                    updatedAttributes = [...currentAttributes, { court_attribute_id: courtAttributeId, value }];
                }

                // Update both selectedCourtAttributes and checkoutData
                const currentData = get().checkoutData;
                set({
                    selectedCourtAttributes: updatedAttributes,
                    checkoutData: currentData ? {
                        ...currentData,
                        court_attributes: updatedAttributes
                    } : null,
                    error: null
                });
            },

            removeCourtAttribute: (courtAttributeId: number) => {
                const currentAttributes = get().selectedCourtAttributes;
                const updatedAttributes = currentAttributes.filter(attr => attr.court_attribute_id !== courtAttributeId);

                const currentData = get().checkoutData;
                set({
                    selectedCourtAttributes: updatedAttributes,
                    checkoutData: currentData ? {
                        ...currentData,
                        court_attributes: updatedAttributes
                    } : null,
                    error: null
                });
            },

            clearCourtAttributes: () => {
                const currentData = get().checkoutData;
                set({
                    selectedCourtAttributes: [],
                    checkoutData: currentData ? {
                        ...currentData,
                        court_attributes: []
                    } : null,
                    error: null
                });
            },

            setProcessing: (processing: boolean) => {
                set({ isProcessing: processing });
            },

            setCompleted: (completed: boolean) => {
                set({
                    isCompleted: completed,
                    isProcessing: false
                });
            },

            setError: (error: string | null) => {
                set({
                    error,
                    isProcessing: false
                });
            },

            clearCheckout: () => {
                set({
                    checkoutData: null,
                    isProcessing: false,
                    isCompleted: false,
                    error: null
                });
            },

            getCheckoutPayload: () => {
                const currentData = get().checkoutData;
                const selectedAttributes = get().selectedCourtAttributes;

                if (!currentData) return null;

                return {
                    ...currentData,
                    court_attributes: selectedAttributes.length > 0 ? selectedAttributes : undefined
                };
            },
            setTotalAmount: (amount: number) => {
                set({ totalAmount: amount });
            },
            setLastTimetoPay: (time: string | null) => {
                set({ lastTimetoPay: time });
            }
        }),
        {
            name: "epic-padel-checkout-store", // unique name for localStorage key
            storage: createJSONStorage(() => localStorage),
            // Only persist the checkout data and selected attributes, not UI state
            partialize: (state: CheckoutState) => ({
                checkoutData: state.checkoutData,
                selectedCourtAttributes: state.selectedCourtAttributes,
            }),
        }
    )
);
