import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { SportType } from "@/types/booking";


// Program interface for booking store
export interface BookingProgram {
    id: number;
    name: string;
    cover_image: string;
    description: string;
    category?: {
        name: string;
    };
    start_date?: string;
    end_date?: string;
    start_time?: string;
    end_time?: string;
    total_max_registrants?: number;
    total_enrolled?: number;
    subscribed_program_price: {
        per_class_price: number;
        full_price: number;
    };
    programClasses: Array<{
        date: string;
        start_time: string;
        end_time: string;
    }>;
}

// Program booking details interface
export interface ProgramBookingDetails {
    programId: number;
    sessionType: string;
    selectedClassIds: number[];
    selectedClasses: Array<{
        id: number;
        date: string;
        start_time: string;
        end_time: string;
    }>;
}

// Court booking details interface
export interface CourtBookingDetails {
    court_id: number;
    sub_court_id: number;
    booking_price_id: number;
    start_time: string;
    end_time: string;
    split: boolean;
    // Additional display data for checkout
    court_name?: string;
    sub_court_name?: string;
    date: string;
    time: string;
    duration: string;
    price: number;
    is_peak_time: boolean;
    attributes?: Array<{
        court_attribute_id: number;
        value: number;
    }>;
    guest_users?: Array<number>;
    redeem?: boolean;
}

// Lesson booking details interface
export interface LessonBookingDetails {
    lesson_id: number;
    instructor_id: number;
    lesson_type_id: number;
    type: string;
    start_time: string;
    duration: number;
    sub_court_id: number;
    // Additional display data for checkout
    date: string;
    time: string;
    players: number;
    lesson_name?: string;
    instructor_name?: string;
    sub_court_name?: string;
    participants?: Array<number>;
    split: boolean;
}

// Instructor booking details interface
export interface InstructorBookingDetails {
    instructor_id: number;
    lesson_type_id: number;
    type: string;
    start_time: string;
    duration: number;
    sub_court_id: number;
    // Additional display data for checkout
    date: string;
    time: string;
    players: number;
    lesson_type_name?: string;
    instructor_name?: string;
    sub_court_name?: string;
    participants?: Array<number>;
    split: boolean;
}

interface BookingState {
    selectedSport: SportType;
    selectedLocation: string;
    selectedProgram: BookingProgram | null;
    programBookingDetails: ProgramBookingDetails | null;
    courtBookingDetails: CourtBookingDetails | null;
    lessonBookingDetails: LessonBookingDetails | null;
    instructorBookingDetails: InstructorBookingDetails | null;
    setSelectedSport: (sport: SportType) => void;
    setSelectedProgram: (program: BookingProgram | null) => void;
    setProgramBookingDetails: (details: ProgramBookingDetails | null) => void;
    setCourtBookingDetails: (details: any | null) => void;
    setLessonBookingDetails: (details: LessonBookingDetails | null) => void;
    setInstructorBookingDetails: (details: InstructorBookingDetails | null) => void;
    setSelectedLocation: (location: string) => void;
    addAttributes: (attributes: Array<{
        court_attribute_id: number;
        value: number;
    }>) => void;
    replaceAttributes: (attributes: Array<{
        court_attribute_id: number;
        value: number;
    }>) => void;
    addGuestUsers: (guestUsers: Array<number>) => void;
    addLessonParticipants: (participants: Array<number>) => void;
    addInstructorParticipants: (participants: Array<number>) => void;
    clearProgramBooking: () => void;
    clearCourtBooking: () => void;
    clearLessonBooking: () => void;
    clearInstructorBooking: () => void;
    clearAllBookings: () => void;
}

export const useBookingStore = create<BookingState>()(
    persist(
        (set, get) => ({
            selectedSport: "Padel",
            selectedLocation: "charlotte",
            selectedProgram: null,
            programBookingDetails: null,
            courtBookingDetails: null,
            lessonBookingDetails: null,
            instructorBookingDetails: null,
            setSelectedSport: (sport) => set({ selectedSport: sport }),
            setSelectedProgram: (program) => set({ selectedProgram: program }),
            setProgramBookingDetails: (details) => {
                console.log("program booking details", details);
                set({ programBookingDetails: details })
            },
            setCourtBookingDetails: (details) => {
                const currentDetails = get().courtBookingDetails;
                console.log("court booking details", details);
                if (currentDetails) {
                    set({
                        courtBookingDetails: {
                            ...currentDetails,
                            ...details
                        }
                    });
                } else {
                    set({ courtBookingDetails: details });
                }
            },
            setLessonBookingDetails: (details) => {
                const currentDetails = get().lessonBookingDetails;
                console.log("lesson booking details", details);
                if (currentDetails) {
                    set({
                        lessonBookingDetails: {
                            ...currentDetails,
                            ...details
                        }
                    });
                } else {
                    set({ lessonBookingDetails: details });
                }
            },
            setInstructorBookingDetails: (details) => {
                const currentDetails = get().instructorBookingDetails;
                console.log("instructor booking details", details);
                if (currentDetails) {
                    set({
                        instructorBookingDetails: {
                            ...currentDetails,
                            ...details
                        }
                    });
                } else {
                    set({ instructorBookingDetails: details });
                }
            },
            addAttributes: (attributes) => {
                const currentDetails = get().courtBookingDetails;
                if (currentDetails) {
                    set({
                        courtBookingDetails: {
                            ...currentDetails,
                            attributes: attributes
                        }
                    });
                }
            },
            replaceAttributes: (attributes) => {
                const currentDetails = get().courtBookingDetails;
                if (currentDetails) {
                    set({
                        courtBookingDetails: {
                            ...currentDetails,
                            attributes: attributes
                        }
                    });
                }
            },
            addGuestUsers: (guestUsers) => {
                const currentDetails = get().courtBookingDetails;
                if (currentDetails) {
                    set({
                        courtBookingDetails: {
                            ...currentDetails,
                            guest_users: [...guestUsers]
                        }
                    });
                }
            },

            addLessonParticipants: (participants) => {
                const currentDetails = get().lessonBookingDetails;
                if (currentDetails) {
                    set({
                        lessonBookingDetails: {
                            ...currentDetails,
                            participants: [...participants]
                        }
                    });
                }
            },

            addInstructorParticipants: (participants) => {
                const currentDetails = get().instructorBookingDetails;
                if (currentDetails) {
                    set({
                        instructorBookingDetails: {
                            ...currentDetails,
                            participants: [...participants]
                        }
                    });
                }
            },

            clearProgramBooking: () => set({
                selectedProgram: null,
                programBookingDetails: null
            }),
            clearCourtBooking: () => set({
                courtBookingDetails: null
            }),
            clearLessonBooking: () => set({
                lessonBookingDetails: null
            }),
            clearInstructorBooking: () => set({
                instructorBookingDetails: null
            }),
            clearAllBookings: () => set({
                selectedProgram: null,
                programBookingDetails: null,
                courtBookingDetails: null,
                lessonBookingDetails: null,
                instructorBookingDetails: null
            }),
            setSelectedLocation: (location) => set({ selectedLocation: location })
        }),
        {
            name: "epic-padel-booking-store", // unique name for localStorage key
            storage: createJSONStorage(() => localStorage),
            // Only persist the booking data, not the actions
            partialize: (state) => ({
                selectedSport: state.selectedSport,
                selectedProgram: state.selectedProgram,
                programBookingDetails: state.programBookingDetails,
                courtBookingDetails: state.courtBookingDetails,
                lessonBookingDetails: state.lessonBookingDetails,
                instructorBookingDetails: state.instructorBookingDetails,
            }),
        }
    )
);
